<?php

namespace App\Observers;

use App\User;

class UserObserver
{
    /**
     * Handle the User "created" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function created(User $user)
    {
        $place_holders = ["{{guest_first_name}}"];
        $values = [$user->first_name ?? ""];
        (new \App\Services\NotificationService())->sendNotification("welcome_to_luxustars", $user, $place_holders, $values);
    }

    /**
     * Handle the User "updated" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function updated(User $user)
    {
        // User completes full verification (phone phone_verified_at + ID in one step)
        if ($user->isDirty('identity_verified') && $user->identity_verified == 'verified' && $user->phone_verified_at != null) {
            $place_holders = ["{{guest_first_name}}"];
            $values = [$user->first_name ?? ""];
            (new \App\Services\NotificationService())->sendNotification("verification_complete", $user, $place_holders, $values);
        }
        // change password
        if ($user->isDirty('password')) {
            $place_holders = ["{{guest_first_name}}"];
            $values = [$user->first_name ?? ""];
            (new \App\Services\NotificationService())->sendNotification("password_updated", $user, $place_holders, $values);
        }
    }

    /**
     * Handle the User "deleted" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function deleted(User $user)
    {
        //
    }

    /**
     * Handle the User "restored" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function restored(User $user)
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function forceDeleted(User $user)
    {
        //
    }
}
