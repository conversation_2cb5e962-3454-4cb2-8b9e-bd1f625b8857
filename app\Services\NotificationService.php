<?php

namespace App\Services;

use App\Models\NotificationTemplate;
use App\Notifications\InAppNotification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class NotificationService
{
    public function sendNotification($key, $user, array $placeholders, array $values)
    {
        $template = NotificationTemplate::where('key', $key)->first();
        if (!$template) {
            return;
        }

        // Replace placeholders in the message
        $short = $this->replacePlaceholders($template->short, $placeholders, $values);
        $expanded = $this->replacePlaceholders($template->expanded, $placeholders, $values);
        $template->short = $short;
        $template->expanded = $expanded;
        // $message = $this->replacePlaceholders($template->message, $placeholders, $values);

        // Send in-app notification if enabled
        if ($template->is_active && $template->app) {
            $user->notify(new InAppNotification($template));
        }

        // Send email notification if enabled
        if ($template->is_active && $template->email) {
            Mail::send('email-templates.notification-template', compact("template"), function ($message) use ($user, $template) {
                $message->to($user->email)
                    ->subject($template->title);
            });
        }

        // Optionally, you can add logic for other channels like SMS, Push notifications, etc.
        // Example for SMS or other channels can be added here.
    }

    private function replacePlaceholders($message, $placeholders, $values)
    {
        return str_replace($placeholders, $values, $message);
    }
}
