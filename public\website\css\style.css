@import url('https://fonts.googleapis.com/css2?family=Epilogue:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css");

:root{
    --light-grey: #4A4A4A;
    --header-black: #171717;
    --btn-yellow: #FFCE32;
    --light-yellow: #FCDF73;
    --cal-grey: #F4F4F4;
    --white: #FFF;
    --black: #000;
    --search-grey: #2D2D2D;
    --srh-text: #9B9B9B;
    --border-grey: #D9D9D9;
    --grey: #E6E6E6;
    --scrollbar: #EEE;
    --comment-grey: #E9E9E9;
    --blue: #0085FF;
    --swiper-navigation-size: 18px;
    --dark-yellow: #FED402;
    --light-blue: #F2F8F2;
    --red: #E1505E;
}

*{ margin: 0; padding: 0;}
body{ color: var(--light-grey); background-color: #fff; font-family: 'Poppins';  }
html{ overflow-x: hidden;  }

@font-face { font-family: 'Poppins-Medium'; src: url('../fonts/Poppins-Medium.ttf');}
@font-face { font-family: 'Poppins-Bold'; src: url('../fonts/Poppins-Bold.ttf');}
@font-face { font-family: 'Poppins-SemiBold'; src: url('../fonts/Poppins-SemiBold.ttf');}
@font-face { font-family: 'Poppins-Regular'; src: url('../fonts/Poppins-Regular.ttf');}


/* ----------Scrollbar------------------*/

::-webkit-scrollbar {  width: 10px;  background-color: transparent;  }
::-webkit-scrollbar-track {  background-color: transparent;  }
::-webkit-scrollbar-thumb {  background-color: #FCDF73;  border-radius: 20px;  }
::-webkit-scrollbar-thumb:hover {  background-color: #FFCE32;  }

.scroll-border-radius::-webkit-scrollbar-thumb { border-top-left-radius: 20px; border-top-right-radius: 20px;}
.no-top-border-radius::-webkit-scrollbar-thumb { border-top-left-radius: 0; border-top-right-radius: 0;}
.no-bottom-border-radius::-webkit-scrollbar-thumb { border-bottom-left-radius: 0; border-bottom-right-radius: 0;}

/*-------Transparent Scrollbar----------*/
.chat-sec-1 .chat_body::-webkit-scrollbar-thumb, .chat-sec-1 .all_chat::-webkit-scrollbar-thumb{ background-color: transparent;}


 /* Loader */
 #sec-loader .load { position: fixed; background-color: #fff; z-index: 999999; display: flex; align-items: center; justify-content: center; top: 0; left: 0; height: 100%; width: 100%; margin: auto;}
 #sec-loader .load img { height: 120px;width: 120px;object-fit: contain;}


/* ----------Global CSS------------------*/

h1 {  font-size: 50px;  } /* change this*/
h2 {  font-size: 38px;  font-weight:500;}
h3 {  font-size: 34px;  font-weight:500;}
h4 {  font-size: 25px;  font-weight: 600;}
h5, .fs-20 {  font-size: 20px;  font-weight: 500;}
h6 { font-size: 17px; font-weight: 500;}
p, .fs-16 { font-size: 16px;  font-weight: 400; line-height: 1.5; word-wrap: break-word; }
.fs-66 { font-size: 66px; }
.fs-30 { font-size: 30px; font-weight: 500;}
.fs-26 { font-size: 26px; font-weight: 400;}
.fs-22 { font-size: 22px; font-weight: 500;}
.fs-20 { font-weight: 400;}
.fs-19 { font-size: 19px; font-weight: 600;}
.fs-18 { font-size: 18px; font-weight: 600;}
.fs-15 { font-size: 15px; font-weight: 400;}
.fs-14 { font-size: 14px; font-weight: 400;}
.fs_17 { font-size: 17px; font-weight: 500; font-family: Poppins;}
.fs-12 { font-size: 12px; font-weight: 400;}
.fs-10 { font-size: 10px; }

.lh-60 { line-height: 60px;}
.lh-30 { line-height: 30px}

.bold { font-weight: 700;}
.semi-bold { font-weight: 600;}
.light_bold, .light-bold { font-weight: 500;}
.normal { font-weight: 400;}
.right { float: right;}
a { color: var(--black)}

.button, .button1 { padding: 10px 20px; border-radius: 70px; border: 1px solid var(--light-grey); color: var(--light-grey);  text-decoration: none; font-size: 15px; font-weight: 400;  background-color: var(--white); position: relative; overflow: hidden; display: inline-block;}
.button:not(.transparent):hover, .button1:hover { box-shadow: 0 0 4px -1px var(--light-grey);}
a.button:hover, button.button:not(.transparent):hover:not(.nav-button .button:hover, .modal_btn:hover)  { color: var(--black); background-color: transparent; border: 1px solid var(--btn-yellow); transition: 0.55s; }
.nav-button .button:hover { background-color: #fff;}
.nav-button { padding-left: 15px;}

.button:not(.button.login:before)::before { content: ""; position: absolute; left: 0; top: 0; width: 100%; height: 100%; background-color: var(--btn-yellow);  transform: translateX(-100%);  transition: all .5s;  z-index: -1; }
.button:hover::before { transform: translateX(0);}
.button1 { background-color: var(--btn-yellow); border-color: transparent;}
.button1:hover{ color: var(--black);  /*background-color: white;*/}
.btn_black, .black_btn { background-color: #000; color: #fff;}

.button_red { padding: 12px 20px; border-radius: 70px; border: 1px solid rgb(221, 0, 0); color: #fff;  text-decoration: none; font-size: 15px; font-weight: 400;  background-color: rgb(221, 0, 0); position: relative; overflow: hidden; display: inline-block;}
.button_red:hover {color: #fff;}

.trans_btn { border: 0; background-color: transparent;}

.blink-text { animation: blink 1s linear 2;  }
.badge { top: 0; right: -2px; background-color: var(--btn-yellow); border-radius: 50%; padding-inline: 0; padding-top: 2px; height: 18px; width: 18px; color: #000;}
.badge p { font-size: 10px; margin: 0;}
.default_badge { width: fit-content; padding: 3px 10px; border-radius: 15px;}


@keyframes blink {
    50% { opacity: 0;  }
}

/*-----------Global Classes--------------*/
.white { color: var(--white)}
.color { color: var(--light-yellow)}
.grey { background-color: var(--cal-grey)}
.blue { color: var(--blue)}
.dark-yellow, .yellow { color: var(--dark-yellow)}
.btn-yellow { color: var(--btn-yellow)}
.red { color: var(--red);}
.bg_yellow { background-color: var(--light-yellow)}
.bg_dark_yellow { background-color: var(--dark-yellow)}
.bg_white { background-color: var(--white)}
.bg_black { background-color: #000;}
.b_radius { border-radius: 28px;}
.border_radius { border-radius: 20px;}
.b_border { border: 1px solid #BDBDBD;}
.b_shadow { box-shadow: 0 0 8px 0 rgba(189, 189, 189, 0.25);}
.b_dark_shadow { box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;}
.action_btn { background-color: transparent; border: none;  }
.divider { border-bottom: 1px solid var(--border-grey); }
.divider.listing_data { padding: 30px 0;}
.divider.listing_data:nth-of-type(1) { padding-top: 10px;}
.listing_data { padding: 30px 0 0;}
.swiper-button-next, .swiper-button-prev {  background-color: var(--white);  padding: 30px; color: var(--light-grey); border-radius: 50%;}
.b-none { border: none;}
input:focus-visible, .body input:focus { outline: unset; box-shadow: none;  }
.gap-10 { gap: 40px;}
.padding { padding: 6em 0 10px 0;}
.text-decoration { text-decoration: underline;}

.wh-box input.form-control:invalid /* ,  .modal input:invalid:focus */ { border: 1px solid red;}
p.text-danger { font-size: 12px;}

.fixed { position: sticky; top: 10px; }
.object-fit-cover { object-fit: cover;}
.object-fit-contain { object-fit: contain;}

.cursor-pointer { cursor: pointer; }

.w-90 { width: 90%; }


/*-----------Header--------------*/
.header, .footer { background-color: var(--header-black);}
.header .navbar .nav-item { padding: 0 5px;}
.header .navbar .nav-item>a, .body .navbar-nav .nav-link:focus { color: var(--white); font-size: 15px; font-weight: 400; cursor: pointer; }
.header .navbar .nav-item:hover>a { color: var(--light-yellow); }
.header .search { background-color: var(--search-grey); color: var(--srh-text); border-radius: 40px; width: 28em; padding-block: 2px;}
.header .search input.form-control, .review .search input.form-control { background-color: transparent;  border: none;  font-weight: 400; font-family: "Poppins"; font-size: 14px; color: var(--white);}
.header .search input.form-control::placeholder { color: var(--dark-yellow); font-size: 12px;}
.header .search .btn { box-shadow: none; }
.header .search .btn i { font-size: 14px; }

.header .user_img img { height: 45px; width: 45px; border-radius: 50%; object-fit: cover; }
.header button.button { padding: 10px 28px;}
.header .button.login, .header .button1.login { padding: 10px 40px; color: var(--black); }
i.fa-heart { font-size: 25px;  }
.header a.navbar-brand { height: 60px; width: 200px; margin-block: auto; }
.header a.navbar-brand img { height: 100%; width: 100%; object-fit: contain;}
.header .search_parent { position: relative;}
.header .search_dropdown { position: absolute; top: 30px; background-color: #fff; border-radius: 8px; padding: 10px 20px; transform: translate(15px, 14px); box-shadow: 0 0 10px rgb(0 0 0 / 17%); width: 28em; z-index: 99999999;}
.header .search_dropdown .listing_search_item_wrapper.divider:last-child { border: 0; }
.header .search_dropdown .listing_search_item_wrapper .listing_search_item:hover {  background: var(--btn-yellow)}
.header .search_dropdown .listing_search_item_wrapper .listing_search_item, .header .search_dropdown .listing_search_item .listing_search_image { border-radius: 10px;}
.header .search_dropdown .listing_search_item_wrapper .listing_search_item .listing_search_title { color: #000;}


.header .lang-pic { padding-block: 8px;}
/* .header .globe #languageDropdown, .header .globe #currencyDropdown {display: inline-block;height:25px;width: 25px;padding-top: 10px;margin-top: 7px;} */
/* .header .globe #languageDropdown img, .header .globe #currencyDropdown img { height: 100%; width: 100%; object-fit: contain; border-radius: 0;} */
.header .lang-pic .flag_img img { height: 25px; width: 25px;}
/* .header .cart img { height: 22px; width: 24px;} */
/* .header .inbox img { height: 21px; width: 26px;} */
.header .navbar-nav .nav-item.active .nav-link img { filter: drop-shadow(0 100px 0 var(--btn-yellow)); transform: translateY(-100px); }
.header .navbar-nav .nav-item.active .nav-link svg { fill: var(--btn-yellow); }

.header .dropdown-menu { border-radius: 15px; overflow-x: hidden;}
.currency_list .dropdown-menu { max-height: 400px; width: 300px; overflow-x: hidden;}
.currency_list .dropdown-menu input.search_curreny { max-width: 95%;}

.header .dropdown-menu .dropdown-item:focus, .header .dropdown-menu .dropdown-item:hover { background-color: var(--btn-yellow);}

.header .navbar .nav-item img, .header .navbar .nav-item svg { vertical-align: top;}
.header .navbar .navbar-collapse { flex-grow: 0; }


/*------------------------Footer-------------------------------*/
.footer .footer-head{ font-family: Epilogue; padding-bottom: 15px;}
.footer ul:not(.social-icons) li > a{ color: var(--white);  font-family: Epilogue;  text-decoration: none;  line-height: 50px;  font-size: 16px;  transition: transform .8s ease-in;  }
.footer li:hover>a{ color: var(--light-yellow);}
.footer .footer-social .icon {  font-size: 24px;  color: var(--light-yellow);  border-radius: 50%;  border: 2px solid var(--btn-yellow);  height: 50px;  width: 50px;  text-align: center; padding-top: 4px;  }
.footer .footer-social .social-icons>li:hover img { transition: transform .5s ease-in-out;  transform: scale(1.1) ;  }
.footer .footerLinks ul>li:hover{ transform: translate(5px, 0);}
.footer .foot-copyright { border-top: 1px solid var(--white); }
.footer .navbar-brand { display: inline-block; width: 260px;}
.footer .navbar-brand img { height: 100%; width: 100%; object-fit: contain;}
.foot-copyright .white:hover { color: var(--btn-yellow);}
.footer .footer-social .social-icons>li:hover .download_app { transform: scale(1.05); }
.footer .footer-social .social-icons .download_app { border-radius: 60px;}

/*----------------Detail Page---------------------*/
.sec1_detail .swiper-slide img{ border-radius: 20px ; height: 98%; object-fit: cover;}
.map iframe{  border-radius: 20px;}
.sec1_detail .info, .sec2_detail .book .info{  border-radius: 20px; box-shadow: 0 4px 44px 0 rgba(0, 0, 0, 0.05);}
.sec1_detail .info { min-height: 360px;}
.sec1_detail .info h5.py-3 span.rating, .sec2_detail .info h5 span.rating {  float: right;}
.sec1_detail .info.medical_info .company_info p { max-width: 300px;}
.sec1_detail h2, .sec2_detail h3,.sec2_detail h4, .sec2_detail h5, .sec2_detail h6  { width: fit-content;}
/* .sec1_detail .mySwiper2{height: 83%;} */
.sec2_detail .info h5 { width: unset;}
.sec2_detail .catg{  padding: 10px 20px;  border: 1px solid var(--border-grey);  margin-right: 15px;  border-radius: 10px; text-transform: capitalize;  list-style: none;    }
.sec2_detail .categories{ /*padding-bottom: 15px;*/  padding-left: 0;  flex-wrap: wrap;  }
/* .sec2_detail .inner_detail {  border-top: 1px solid var(--grey);  border-bottom: 1px solid var(--grey);  padding-bottom: 15px;  } */
.sec2_detail .book h5 { padding-inline: 0;}
.sec-2-detail .col-md-12 .list_desc { display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; word-break: break-word; margin-bottom: 10px;}
.sec-2-detail .col-md-12 .list_desc p:last-child {  margin: 0;}
.sec2_detail .pay_info ul, .sec2_detail div.col-md-12 {padding-inline: 0;}
.sec2_detail .pay_info li{  padding-bottom: 10px;  list-style: none;  }
.sec2_detail .button.reserve{padding: 12px 35px;}
.sec2_detail .fa-check-square{color: var(--light-yellow); font-size: 18px;}
.sec2_detail ol { counter-reset: item; position: relative;}
.sec2_detail ol>li{  position: relative;  list-style: none; counter-increment: item;}
.sec2_detail ol>li::before, .sec2_detail ol>li::after, .sec2_detail ol::after{ content: ""; position: absolute; left: -50px;  top: 0;}
/* .sec2_detail ol>li::before{  content: counter(item) '';  z-index: 2; color: var(--white);  left: -41px;  top: 2px;  } */

.sec2_detail ol>li::before { content: counter(item) ''; z-index: 2; color: var(--white); left: -48px; top: 2px; background-color: var(--light-grey); width: 35px; height: 35px; border-radius: 50%; display: flex; justify-content: center; align-items: center; }
.sec2_detail ol>li:first-child:before, .sec2_detail ol>li:last-child:before {content: "\f3c5"; font-family: "Font Awesome 5 Free"; font-weight: 900; color: #000; background-color: #fed402;}

/* .sec2_detail ol>li:nth-child(1)::before, .sec2_detail ol>li:last-child::before{ content: '';} */
.sec2_detail ol>li:not(:last-child)::after { content: ""; background-image: url(../images/timeline_line_dot.svg); background-repeat: repeat; height: 100%; width: 5px; left: -33px; top: 10px; background-size: contain;}
/* .sec2_detail ol>li:nth-child(1)::after, .sec2_detail ol>li:last-child::after{  content: "\f3c5";  font-family: "Font Awesome 5 Free";  width: 40px;  height: 40px;  padding: 6px 8px 8px 14px;  left: -55px;  background-color: var(--light-yellow);  } */
/* .sec2_detail ol::after { content: '|';  background-image: url(../images/Lines.png); background-image: url(../images/timeline_line_dot.png); background-repeat: no-repeat; background-size: 3px 93%;  display: block;  width: 100%;  height: 100%;  left: 12px;  top: 2px;  z-index: -1;  } */
/* .sec2_detail form .rent_time>input{} */

/* .sec2_detail ol>li:last-child::after { content: ""; background-image: url(../images/timeline_line_dot.png); background-repeat: repeat; height: 100%; width: 2px; left: -32px; top: 10px; background-color: black; } */
.sec2_detail .custom_btns .end_time {position: relative;}
.sec2_detail .custom_btns .end_time:before {content: ""; left: 16px; top: 0; bottom: 0; margin: auto 0; position: absolute; display: block; background-color: black; height: 2px; width: 25px;}
.sec2_detail .custom_btns .end_time:after {content: ""; left: 16px; top: 0; bottom: 18px; margin: auto 0; position: absolute; display: block; background-color: black; height: 20px; width: 2px;}

.sec2_detail .custom_btns .start_time {position: relative;}
.sec2_detail .custom_btns .start_time:before {content: ""; left: 16px; top: 0; bottom: 0; margin: auto 0; position: absolute; display: block; background-color: black; height: 2px; width: 25px;}
.sec2_detail .custom_btns .start_time:after {content: ""; left: 16px; top: 47px; bottom: 0; margin: auto 0; position: absolute; display: block; background-color: black; height: 70px; width: 2px;}

.sec2_detail .book .info .book_timing { width: 46%; }
.sec2_detail .book .info .book_timing input { width: 90%; color: #4a4a4a;}
.sec2_detail .book .info .check_date { width: 48%;}

.sec2_detail .book .info select, .sec2_detail .book .info select:focus { appearance: auto; box-shadow: none; outline: 0;}

.sec2_detail .book .info.booking_info { /* min-height: 560px;*/ min-height: 100%; }
.sec2_detail .book .info.booking_info.hourly_booking { min-height: 490px; }
.sec2_detail .book .info.booking_info.select_date { display: flex; justify-content: center; align-items: center; font-weight: 500;font-size: 18px; flex-direction: column;}

.review .modal-header{ border: none;}
.review .modal-all {  max-width: 1050px;  margin: 2rem auto;  overflow: hidden;  border-radius: 20px;  background-color: var(--white);  }
.review .modal-dialog {  max-width: 1000px;  margin-top: 0; /* height: 530px; */ /* overflow: hidden; */  border-radius: 20px;  }
.review .modal-content { /*overflow: scroll;*/  height: 84vh;  border: none;  }
/* .review .btn-close {  font-size: 10px;  border: 1px solid var(--light-grey);  border-radius: 50%;  } */
.review .search input.form-control { color: var(--black)}
.leave_review input.chat-input { width: 86%; border-radius: 40px; padding: 10px 30px; height: 50px; outline: none; border: 1px solid var(--comment-grey);}
.leave_review button.button { padding: 0 25px; height: 45px;}
.leave_review .rating i { color: var(--light-yellow);}
.review .search { border-radius: 40px; border: 1px solid var(--grey);}
.review .modal-body {overflow: hidden; overflow-y: auto;}
.review ::-webkit-scrollbar {  width: 10px;  background-color: var(--scrollbar); border-radius: 20px;  }
.review ::-webkit-scrollbar-track {  background-color: transparent;  }
.review ::-webkit-scrollbar-thumb {  background-color: var(--light-yellow);  border-radius: 20px;  }
.review ::-webkit-scrollbar-thumb:hover {  background-color: var(--btn-yellow);  }

body:has(.review.modal.show) { max-height: 100vh; }

/*----------------------------Modal Reviews----------------------*/
.modal.login:not(.otp_modal) .modal-dialog{ margin: 8rem auto;}
.modal.login.signup .modal-dialog{ margin: 1.75rem auto;}
.modal.login .modal-content, .modal.report .modal-content { border-radius: 20px;}
.modal .luxu{font-weight: 700;}
.modal .button.login { width: 93%; background-color: var(--btn-yellow);  color: var(--black); border-color: var(--btn-yellow);  border-radius: 12px; margin-inline: 16px;  }
.modal input, .modal textarea, .modal select {  border:  1px solid var(--light-grey);  padding: 10px 20px;  font-size: 14px;  border-radius: 12px;  }
.modal .forgot{float: right;}
.modal.login .form-outline.mb-4.d-inline-block { width: 49%;  }
.modal.login .form-outline.mb-4.d-inline-block:nth-child(1){  margin-right: 10px;  }
.modal .form-control.is-invalid + i { right: 30px; }
/*.modal.login .form-outline.mb-4.d-inline-block:nth-child(2){  margin-left: 5px;  }*/
.modal .iti{ display: block;}
.modal .iti__country-list {max-width: 420px;}
.modal .iti__country-list::-webkit-scrollbar { height: 8px; width: 8px;}
#login .button.login { width: 100%; margin-inline: 0;}
.iti { width: 100%;}

.modal button.not_btn{ background-color: transparent; border: none;}
.modal.login .code input { display: inline;  width: 50px;  margin-right: 10px; }
.modal.verify .modal-dialog{ max-width: 35em;  }
.modal.verify fieldset:not(:first-of-type), .modal.signup fieldset:not(:first-of-type) { display: none }
.modal.report .modal-header, .modal.report .modal-footer { border: 0;}
.modal.report .modal-footer .button1  { width: 100%;}
.modal.report .modal-footer .button1:hover { color: #fff;}
.signup .term-condition{text-align: left;padding-left: 0; display: flex; gap: 10px;}
.signup .term-condition input{border-radius: .25em;padding: 7px;float: unset;margin-left: 0;}
.signup .term-condition label>a{color: #e3b911;}
#resend-email-otp-btn[disabled] { color: var(--srh-text);  cursor: no-drop;}
body:has(.review-reoprt.show) .modal:not(.review-reoprt.show) {  filter: brightness(0.5);}

body .verify_email_btn { all: unset; cursor: pointer; background-color: var(--btn-yellow); margin-left: 10px;  border-radius: 60px; font-size: 14px; min-width: 55px; }

#sign-up-error ul li { padding-left: 25px; position: relative; font-size: 13px;}
#sign-up-error ul li:before { content: '\f057'; position: absolute; left: 0; color: #cb1c2c; font-weight: 600; font-family: 'Font Awesome 5 Free'; }


/*----------------Calendar------------------*/

.sec2_detail .cal .pignose-calendar{  margin: unset;  max-width: 400px;  border-bottom: 1px solid #E6E6E6;  }
.sec2_detail .pignose-calendar, .sec2_detail .pignose-calendar .pignose-calendar-top{  box-shadow: none;  border: none;  background-color: transparent;  padding: 0;  }
.sec2_detail .pignose-calendar .pignose-calendar-top{ padding: 1.6em 0;}

.sec2_detail .cal .pignose-calendar-week, .sec2_detail .cal .pignose-calendar-week.pignose-calendar-week-sat, .sec2_detail .cal .pignose-calendar-week.pignose-calendar-week-sun{  background-color: var(--grey);  color: var(--light-grey);  border: none;  font-size: 15px;  font-weight: 400;  }
.sec2_detail .cal .pignose-calendar .pignose-calendar-unit a{ color: var(--light-grey)}
.sec2_detail .cal .pignose-calendar .pignose-calendar-top .pignose-calendar-top-month, .pignose-calendar .pignose-calendar-top .pignose-calendar-top-year{  display: inline;  font-size: 19px;  font-weight: 600;  color: var(--light-grey);  text-transform: capitalize;  }
.sec2_detail .cal .pignose-calendar .pignose-calendar-unit.pignose-calendar-unit-active a{  background-color: var(--light-yellow);  color: var(--white);  }
.sec2_detail .calendar .pointer { cursor: pointer;  user-select: none;  }
.sec2_detail .calendar p {  line-height: 1.5em;  margin: 0;  color: #444;  }
.sec2_detail .calendar {  background: #fff;  color: #777;  }
.sec2_detail .calendar li {  float: left;  width: calc(100% / 7);  text-align: center;  padding: .65em 0;  cursor: pointer;  }
.sec2_detail .calendar li.empty {  pointer-events: none;  }
.sec2_detail .calendar li:hover {  color: var(--light-yellow);  }
.sec2_detail .calendar li.active,
.sec2_detail .selected-range,
.sec2_detail .calendar li.active:hover { /*background-color: var(--light-yellow);*/  color: #fff;  position: relative;  z-index: 0;  }
.sec2_detail .calendar li:nth-child(-n+6) {  color: #777;  }
.sec2_detail .calendar li.active::before {  content: '';  width: 35px;  height: 35px;  background-color: var(--light-yellow);  position: absolute;  top: 5px;  left: 8px;  z-index: -1;  border-radius: 50%;  }

.calendar  .selected-range:before,
.calendar  .selected-range:after {  content: '';  width: 40px;  height: 35px;  background-color: var(--cal-grey);  color: var(--white);  position: absolute;  top: 5px;  right: -10px;  z-index: -2;  }
.sec2_detail .selected-range{color: var(--light-grey)}
.calendar .selected-range:before {  left: -10px;  }

/*-----------------Tour----------------*/
.sec2_tour .inner_detail .info.py-2.ps-5 {  border-left: 2px solid var(--grey);  }
.v_divide{position: relative;}
.v_divide:before {  content: '|';  width: 10px;  height: 100%;  z-index: -1;  position: absolute;  top: 0;  left: -6px;  }
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {  -webkit-appearance: none;  margin: 0;  }
.counter button{  text-decoration: none;  color: var(--black);  }
.counter input{  width: 100%;  border: none;  }

/*-----------Index Topbar--------------*/
.topbar{ margin-top: 20px;  }
.topbar .top_box1 {  display: flex;  padding-top: 20px;  gap: 30px;  }
.topbar .top_box1 .box input.radio_btn { display: none; }
.topbar .top_box1 .box label {  cursor: pointer;  border-radius: 60px;  background-color: #fff;  transition: 0.4s ease-in; box-shadow: 0 8px 21px 0 rgba(0, 0, 0, 0.07);  padding: 14px 20px;  font-size: 16px;  width: 225px;  border: 2px solid #fff;  }
.topbar .top_box1 .box label:hover {  background-color: var(--btn-yellow); border-color: var(--btn-yellow); transition: 0.4s ease-in;}
.topbar .top_box1 .find_btn .radio_img img {  display: block;  mix-blend-mode: difference;  }
.topbar .top_box1 .find_btn{color: var(--srh-text);}
/* .topbar .top_box1 .box>input:checked+label {  border: 2px solid #FFCE32; color: #FFCE32; } */
.topbar .top_box1 .box>input:checked+label {border: 2px solid #FFCE32; color: black; background: #FFCE32;}

.topbar .top_box1 .box>input:checked + label svg path {fill: #FFCE32;}
.product_box input + label svg path {fill: rgba(0, 0, 0, 0.4);}
.heart:checked + label svg path {fill: #FFCE32;}

/*.topbar .top_box1 .box>input:checked+ .radio_img svg path { fill: #FFCE32;}*/
.topbar form .top_box2{  display: flex;  margin-top: 10px; gap: 30px; flex-wrap: wrap; }
.topbar .top-rw{  align-items: center;  }
.topbar .col-md-1 { padding:0; }
.topbar .radio_img { padding-bottom: 8px; height: 30px; width: 25px; }
.topbar .radio_img img { height: 100%; width: 100%; object-fit: contain; }
/* .s-btn-icon {   display: inline-block;   max-width: fit-content;  position: relative;} */
/* .s-btn-icon i { position: absolute; right: 20px; top: 31%; color: #ffce32; cursor: pointer;} */
.search[disabled] + i { cursor: no-drop;}
.topbar form .top_box2 label{  color: var(--light-grey);  font-family: Poppins;  font-size: 16px;   /* changed this */  font-style: normal;  font-weight: 400;  padding-left: 10px;  }
.topbar form .top_box2 .box2 input{  padding: 15px;  border: none;  border-radius: 64.264px;  background: var(--white);  box-shadow: 0 8.568594932556152px 22.49256134033203px 0 rgba(0, 0, 0, 0.07);  height: 47px;  width: 97%;  }
.topbar form .top_box2 .box2 input::placeholder{  color: var(--header-black);  font-size: 14px;  font-weight: 400;  }
/* .topbar form .top_box2 .box2{  width: 36%;  } */
.topbar form .top_box2 .box3 input{  font-size: 13px;  padding: 15px 50px 15px 15px;  border: none;  border-radius: 64.264px;  background: var(--white);  box-shadow: 0 8.568594932556152px 22.49256134033203px 0 rgba(0, 0, 0, 0.07);  height: 47px;  width: 100%;  }
.topbar form .top_box2 .submit .button{ padding: 12px 40px;}
.topbar form .top_box2 .submit .search { padding-right: 50px;}
.topbar .top_box3{  margin-top: 15px;  }
.topbar .top_box3 button{  padding: 17px 30px;  margin-left: -23px;  }
.d-btn {   width: 100%;   position: relative;}
.d-btn i {  position: absolute;  right: 16px;  top: 20%; color: var(--btn-yellow);}
#search-box .top_box2 .box3{flex-grow: 1;}
/* #search-box .submit{flex-grow: 1; display: flex;   gap: 20px;} */
.search[disabled] { /* background-color: var(--border-grey); */ opacity: 0.6; cursor: no-drop;}
.search[disabled]:hover { box-shadow: none;}


/* .topbar form .box3 .search_input:after { content: ''; display: inline-block;  width: 30px; height: 30px;  background-image: url('../images/calendar.svg'); background-repeat: no-repeat; background-position: center;} */
/* .topbar form .box3:nth-child(1) .search_input:after { content: ''; display: inline-block;  width: 30px; height: 30px;  background-image: url('../images/location-dot.svg'); background-repeat: no-repeat; background-position: center;} */
::-webkit-calendar-picker-indicator {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 24 24"><path fill="%23FFCE32" d="M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z"/></svg>');
  }
.topbar form .box3 .loc { top: 36px; right: 25px;}
.topbar form .box3 .loc i { color: var(--btn-yellow); cursor: pointer;}
.topbar form .box3 input[type=date] { cursor: pointer;}

.pika-button:hover, .pika-row.pick-whole-week:hover .pika-button, .is-selected .pika-button, .has-event .pika-button { background-color: var(--btn-yellow) !important;}
.pika-table abbr { text-decoration: none;}
.is-today .pika-button { color: var(--btn-yellow);}

/*-----------Index Product--------------*/
.product{ /* padding-top: 50px; */ margin-bottom: 30px;  margin-left: auto;  margin-right: auto;  }
.product .product_box{  margin-bottom: 40px;  transition: all .3s ease-in-out; }
.product_box a { color: unset; text-decoration: none; }
.product .product_box .slide-img { height: 18em;}
.product .product_box img{  width: 100%;  border-radius: 18px; height: 100%; object-fit: cover; }
.product .product_box img:hover, .property-image img:hover{filter: brightness(0.8);}
.product .product_box:hover{transform: scale(1.01);}
.product .product_box .rating { display: flex; justify-content: space-between; flex-wrap: wrap; gap: 8px; padding-top: 20px;  }
.product .product_box .span-italic {  font-style: italic; }
.product .product_box span { font-weight: 600;  font-size: 16px; font-family: Epilogue;  }
.product .Load_more{  text-align: center;  }
.product .Load_more button{  padding: 20px 40px;  }

.product .product_box span.swiper-pagination-bullet.swiper-pagination-bullet { color: #fff !important; background-color: #fff !important; opacity: 0.85; }
.product .product_box .product_wish { position: absolute; top: 15px; z-index: 3; width: 100%;}
.product .product_box .product_img { position: relative; }
.product .product_box .product_wish .client { height: 45px; width: 45px; border-radius: 50%;}
.product .product_box .product_wish .client img { border-radius: 50%;}
.product .product_box .product_wish i { color: #fff;}
.product .product_box .product_heading, .main_cart .list_product_heading { display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden; margin: 0; height: fit-content; width: 80%; font-size: 18px; }
.product .product_box .product_heading:first-letter, .main_cart .list_product_heading:first-letter { text-transform: capitalize;}
.main_cart .list_product_heading { width: 75%;}
.main_cart:has(.new_listing) .list_product_heading { width: 70%;}

.product_img .swiper-button-next, .product_img .swiper-button-prev { background-color: transparent; color: #FFCE32; padding: 0; }

 /*-----------About Page--------------*/
.about_banner .about-con{  padding-left: 0;  padding-right: 0;  }
.about_banner .about_box img{  width: 100%;  }
.about .about-box2{  text-align: center;  padding-top: 50px;  margin-bottom: 30px;  }
.about .about-box2 h2 {  color: var(--light-grey);  font-style: normal;  font-weight: 700; line-height: normal;  text-transform: capitalize;  font-size: 50px; max-width: 20em; margin: auto; padding-bottom: 30px; /* margin-left: 200px;  margin-right: 200px;  margin-bottom: 30px; */  }
.about .about-box2 a{  padding: 13px 36px;  margin-inline: 4px;  color: var(--light-grey);  transition: .5s ease-in-out;  }
.about .about-box2 .black{  background: var(--black);  color: var(--white);  }
.about .about-box2 a:hover{  padding-right: 25px; transition: .5s ease-in-out;  }
.about .about-box2 a i { padding-left: 2px; transition: .5s ease-in-out;  }
.about .about-box2 a:hover i { padding-left: 25px; transition: .5s ease-in-out;  }
.about .about-box2 a.button1.black:hover {  padding-right: 56px;}
.about .about-box2 .box1 {  padding-top: 70px;  }
.about .about-box2 .box1 h2{  line-height: 45px;  }
.about .about-box2 .box1 p{  line-height: 30px;  }

 /*-----------Contact Page--------------*/
 #adv_filter i{ color: var(--btn-yellow);}
 .irs--round { padding-right: 20px;}
.contact{  padding-top: 50px;  margin-bottom: 30px;  }
.contact .icon_box {  border-radius: 20px; border: 3px solid transparent; transition: 0.3s ease-in; box-shadow: 0 4px 44px 0 rgba(0, 0, 0, 0.05);  padding: 50px 20px;  text-align: center;  }
.contact .icon_box:hover { border-color: var(--btn-yellow); transition: 0.3s ease-in;}
.contact .icon_box p { color: var(--white);  font-family: Epilogue;  text-decoration: none;  line-height: 50px;  font-size: 16px;  }
.contact .icon_box p>i { font-size: 26px;  color: var(--light-grey);  border-radius: 50%;  background: var(--btn-yellow);  border: 2px solid var(--btn-yellow);  height: 50px;  width: 50px;  padding: 17px 20px;  }
.contact .icon_box h5 { margin-top: 30px;  }
.contact .form-box input#subject {    width: 100%;}
.contact .icon_box a { color: var(--light-grey);  font-size: 15px;  font-style: normal;  font-weight: 500;  text-decoration-line: underline; word-wrap: break-word; }
.contact .form-box {  margin-top: 40px;  border-radius: 20px;  background: #FFF;  box-shadow: 0 4px 44px 0 rgba(0, 0, 0, 0.05);  padding: 50px;  }
.contact .form-box h6{  font-size: 32px;  font-weight: 500;  }
.contact .form-box input{  width: 49%;  border-radius: 10px;  border: 1px solid #DADADA;  height: 53px;  margin-bottom: 15px;  padding-left: 20px;  }
.contact .form-box input::placeholder{  font-size: 13px;  font-weight: 400;  color: var(--light-grey);  }
.contact .form-box .pl{  padding-left: 80px;  width: 100%;  }
.contact .form-box input:focus-visible{  border: 1px solid #DADADA;  outline: none;  }
.contact .form-box textarea{  width: 100%;  border-radius: 10px;  border: 1px solid #DADADA;  padding-left: 20px;  padding-top: 20px;  margin-bottom: 15px;  font-size: 13px;  font-weight: 400;  }
.contact .form-box textarea:focus-visible{  border: 1px solid #DADADA;  outline: none;  }
.contact .form-box button{  padding: 14px 44px;  }
.contact .form-box .right{  margin-right: 13px;  }
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {  -webkit-appearance: none;  margin: 0;  }
.contact .form-box .iti__selected-flag { border-radius: 9px 0 0 9px;  background: #F2F2F2;  padding: 0 17px 0 21px;  }
.contact .intl-tel-input, .contact .iti {  width: 49%;  display: inline-flex; height: 53px; margin-bottom: 15px; }
.contact .boxes { gap: 20px 0;}

.iti__selected-flag { border-radius: 9px 0 0 9px; }
body .iti--allow-dropdown input, .iti--allow-dropdown input[type=tel], .iti--separate-dial-code input { padding-left: 60px; }

/*-------------------------Account-setting-page-css------------------*/
.profile-image-div { border-radius: 15%;  position: relative;  margin-block: 2em;  margin-left: 1em;  width: 180px;  height: 180px;  overflow: hidden;}
img#blah { width: 100%;  height: 100%;  object-fit: cover;  object-position: center;  }
.profile-image-div img {  max-width: 180px;  }
.profile-image-div input[type=file] {  padding: 10px;  background: #2d2d2d;  }
.profile-image-div input {  position: absolute;  width: 100%;  top: 0;  height: 100%;  opacity: 0;  z-index: 9999;  cursor: pointer;  left: 0;  }
/* .profile-image-div:after {  content: '';  background-color: var(--btn-yellow); display: block;  width: 16%;  height: 15%;   position: absolute;  bottom: 0; right: 0;  background: url();  cursor: pointer;  background-size: contain;  } */
.pencil_icon { background-color: var(--btn-yellow); border-radius: 10px; height: 30px; width: 30px; text-align: center; padding: 3px;}
.pencil_icon.profile_img_edit { position: absolute; right: 8px; bottom: 8px; }
.pencil_icon.edit-sec { height: 45px; width: 45px; padding: 10px; }
.pencil_icon:hover, .pencil_icon.edit-sec.active { background-color: #000; color: #fff; }

.wh-box { box-shadow: -4px 4px 30px #********; border-radius: 20px;  padding: 30px;  margin-bottom: 2em;  }
.form_field_padding { padding-block: 15px;  }
.form_field_padding input.form-control, .form_field_padding select.form-control { height: 50px; border-radius: 15px; margin-bottom: 5px;  }
.form_field_padding input.form-control:not(.iti input), .form_field_padding select.form-control { padding-inline: 18px; }
.bank_month input.form-control { border: 0; border-radius: 0;}
.bank_month>div {  border: 1px solid #ced4da; overflow: hidden; height: 52px;  padding: 0;  border-radius: 15px;}
input.btn.setting_btn {  padding-block: 14px;  padding-inline: 45px;  margin-left: 5px;  border-radius: 15px;  }
input.btn.setting_btn {  padding-block: 14px;  padding-inline: 45px;  margin-left: 5px;  border-radius: 15px;  background-color: #E9E9E9;  }
input.btn.setting_btn:hover {  background: var(--btn-yellow);  color: var(--white-color);  }
input.btn.setting_btn:not([disabled]) { background-color: var(--btn-yellow); border: 0; }
.dropdown-menu.show { border-radius: 10px; }
.dropdown-item:focus, .dropdown-item:hover { background-color: var(--btn-yellow);}
.det_form.setting_page { padding-block: 50px;  }
.box_head { display: flex;  justify-content: space-between;  }
.box_head a img { height: 45px;  width: 45px;  }
.btn-row button.btn { float: right;  background: var(--btn-yellow);  color: var(--light-grey);  padding-block: 12px;  padding-inline: 15px;  border-radius: 15px;  margin-bottom: 1em;  font-size: 16px;  }
.btn-row button.btn:hover { background: var(--light-grey);  border: 1px solid var(--light-grey);  color: #E9E9E9;  }

/* .sec-2-profileSetting .img {padding: 30px 60px 28px 40px; border-radius: 15px; } */
.verification_webaccount_setting_sec .img {background-color: #FCDF73; border-radius: 22px; padding: 30px 60px 28px 40px; border-radius: 15px; position: relative;}
.verification_webaccount_setting_sec .img::before { content: ''; position: absolute; top: 0; right: 300px; width: 432px; height: 100%; background-image: url(../images/kyc_verification_image.png); background-repeat: no-repeat; background-size: contain; }
.sec-2-profileSetting .button, .sec-2-profileSetting .button:hover { border: 2px solid var(--light-yellow); box-shadow: 0 0 4px -1px var(--light-grey);}


.bank_month { position: relative;}
.bank_month .text-danger { position: absolute;  bottom: -40px; width: 50%;}
.cvc { max-width: 90px;}
.cards_table td { border-top: 1px solid grey;}
.cards_table td:last-child { text-align: end;}

/*Account-setting-page-css- END*/


/*---------------------Bookings-page-css----------------------*/
.bookings .past-current ,.bookings h2{  display: inline-block;}
.bookings .booking_btns{ display: flex;  justify-content: space-between;  align-items: center; }
.style-past-booking { background: var(--light-grey);  color: #fff; }
.style-past-booking:hover { color: var(--white); }
.property-image,.property-asset{ display: inline-block; }
.property-image img.img-fluid { width: 100%; height: 100%; object-fit: cover; border-radius: 20px; }
.property-asset { display: flex;  gap: 30px; width: 100%; }
.main_cart { display: flex;  align-items: start; gap: 20px; padding: 12px; box-shadow: -4px 4px 30px #********;  border-radius: 20px;  margin-block: 1em;  }
.property-image { height: 230px;  width: 430px; }
.property-image img.img-fluid { width: 100%; height: 100%; object-fit: cover; border-radius: 20px;  }
.property-asset { display: flex; gap: 10px; width: 100%; padding-block: 12px; border-bottom: 1px solid lightgray; }
.property-asset2 { display: flex; gap: 10px; width: 100%; padding-block: 15px;  }
.fs-12 p { font-size: 12px; font-weight: 400;}
.cart_text { max-width: 68%;  width: 100%; }
.bookings .search { height: 50px;  border-radius: 25px;  border: 1px solid lightgray;  }
.search input.form-control { border: none; }

.booked_card a { text-decoration: none;}
.booked_card .cart_text .property-asset p:nth-child(1), .booked_card .cart_text .property-asset2 p:nth-child(1) { font-size: 10px;}
.booked_card .cart_text .property-asset2 div:first-child, .booked_card .cart_text .property-asset div:first-child { width: 140px;  /* min-width: 110px; */  /* max-width: 110px; */  }
.booked_card .cart_text .property-asset2 div:nth-child(2), .booked_card .cart_text .property-asset div:nth-child(2) { width: 100px;}
.bookings .search input { width: 90%; }


.wishlist .main_cart h4 { font-size: 20px;}
.wishlist .main_cart .listing_data .parent-box .box { font-size: 10px; padding-inline: 6px;}

/* Wishlist search bar style starts from here */

.wishlist_main_sec .heading_search_wrapper {display: flex; justify-content: space-between; align-items: center;}
.wishlist_main_sec .heading_search_wrapper .heading_wrapper {flex: 3;}
.wishlist_main_sec .heading_search_wrapper .searchBar_wrapper {position: relative; flex: 2;}
.wishlist_main_sec .heading_search_wrapper .searchBar_wrapper input[type="search"] {padding-right: 40px; height: 50px; border: 1px solid lightgray; border-radius: 30px; font-family: 'Poppins'; font-size: 16px; padding-left: 20px;}
.wishlist_main_sec .heading_search_wrapper .searchBar_wrapper input[type="search"]:placeholder {font-family: 'Poppins';}
.wishlist_main_sec .heading_search_wrapper .searchBar_wrapper input[type="search"]:focus {/*border-color: #ffce32;*/ border-color: lightgray;}
.wishlist_main_sec .heading_search_wrapper .searchBar_wrapper i {position: absolute; right: 20px; top: 0; bottom: 0; margin: auto 0; height: fit-content; color: #4A4A4A;}
.wishlist_main_sec .heading_search_wrapper .searchBar_wrapper input[type="search"]::-webkit-search-cancel-button {display: none;}

/* Wishlist seach bar style ends here */


/*-----------Booking Detail Page--------------*/
.book_details { padding-top: 50px;  margin-bottom: 30px;  }
.book_details .book_img img { height: 100%; width: 100%; object-fit: cover; }
.book_details .book_box ul {  padding-left: 0;  }
.book_details .book_box li { padding: 10px 20px;  margin-right: 15px; font-size: 12px; border: 1px solid var(--border-grey);  border-radius: 10px;  list-style: none;  }
.book_details .book_box li span:first-letter { text-transform: capitalize;}
.book_details .book_box .amenities ul li{  padding: 10px 20px;  margin-right: 15px;  border: 1px solid var(--border-grey);  border-radius: 10px;  list-style: none;  display: inline-block;  margin-bottom: 11px;  }
.book_details .book_box h5{  margin-bottom: 15px;  }
.book_details .book_box .price_box p{  display: flex;  justify-content: space-between;  }
.book_details .book_box .price_box h6{  display: flex;  justify-content: space-between;  }
.book_details .book_box .price_box hr{  color: var(--grey); }
.book_details .book-rw{  border-radius: 20px;  background: #FFF;  box-shadow: 0 4px 44px 0 rgba(0, 0, 0, 0.05);  padding: 11px 1px;  margin-top: 30px;  }
.book_details .book_box .book-rw{ align-items: center;  border-radius: 0;  background: var(--white);  box-shadow: none;  padding: 0;  margin-top: 17px;  }
.book_details .trip_box .trip1 {  display: flex;  justify-content: space-between;  margin-bottom: 25px;  }
.book_details .trip_box .trip1 .box1 h5{  line-height: 12px;  font-weight: 400;  }
.book_details .trip_box .trip1 .box1 span{  font-size: 14px;  color: var(--srh-text);  }
.book_details .trip-rw{  margin-bottom: 20px; margin-top: 50px;  }
.book_details .trip-rw .trip_box{  border-radius: 20px;  background: #FFF;  box-shadow: 0 4px 44px 0 rgba(0, 0, 0, 0.05);  padding: 40px;  padding-bottom: 10px;  }
.book_details p{  font-size: 14px;  line-height: 25px;  }
/* .book_details .trip-rw .pay_box {  border-radius: 20px;  background: #FFF;  box-shadow: 0 4px 44px 0 rgba(0, 0, 0, 0.05);  padding: 37px 40px;  } */

/* .book_details .trip-rw .pay_box .parent_select{  border-radius: 10px; border: 1px solid #DADADA;  width: 100%;  height: 53px;  margin-bottom: 15px;  font-size: 13px;  padding-right: 10px;  color: var(--light-grey);  font-weight: 400;  } */
/* .book_details .trip-rw .pay_box .parent_select .select2:after { content: ''; display: inline-block; position: absolute; right: 20px; top: 0; bottom: 0; margin: auto 0; border: 2px solid #000;  width: 10px;  height: 10px; } */

.book_details .trip-rw .pay_box .parent_select { margin-bottom: 15px; }
.book_details .trip-rw .pay_box .parent_select .select2 { width: 100% !important; border: 1px solid #DADADA; border-radius: 10px;  }
.book_details .trip-rw .pay_box .parent_select .select2 *:not(.select2-container--default .select2-selection--single .select2-selection__arrow, .select2-container--default .select2-selection--single .select2-selection__arrow b ) { border: 0; outline: 0;}
.book_details .trip-rw .pay_box .parent_select .select2 .select2-selection { height: 40px; }
.book_details .trip-rw .pay_box .parent_select .select2-container--default .select2-selection--single .select2-selection__arrow { right: 10px; top: 0; bottom: 0; margin-block: auto;}
.book_details .trip-rw .pay_box .parent_select .select2-container--default .select2-selection--single .select2-selection__rendered { line-height: 46px;}
/* .book_details .trip-rw .pay_box .parent_select .select2-container--default .select2-selection--single .select2-selection__arrow { border: 2px;} */

.search_reset { display: none;}

.book_details .trip-rw .pay_box h4 {  margin-bottom: 20px;}
.book_details .trip-rw .pay_box input, .sec2_checkout input{  border-radius: 10px;  border: 1px solid #DADADA;  width: 100%;  height: 53px;  margin-bottom: 15px; padding-left: 10px; }
.book_details .trip-rw .pay_box input[type="submit"], .sec2_checkout input[type="submit"] {  border: 1px solid var(--btn-yellow); background-color: var(--btn-yellow); }
.book_details .trip-rw .pay_box input::placeholder, .sec2_checkout input::placeholder{  font-size: 13px;    color: var(--light-grey); font-weight: 400; }
.book_details .trip-rw .pay_box input:focus-visible, .sec2_checkout input:focus-visible { outline: none; padding-left: 10px; }
.book_details .trip-rw .pay_box select:focus-visible, .sec2_checkout select:focus-visible {  outline: none;  }
.book_details .trip-rw .pay_box .prep{  width: 49%;  margin-right: 6px;  }
.book_details .trip-rw .pay_box .prep2 { width: 49%; }
.book_details .billing-box{  border-radius: 20px;  background: #FFF;  box-shadow: 0 4px 44px 0 rgba(0, 0, 0, 0.05);  margin-bottom: 30px;  margin-top: 26px;  padding: 40px;  }
.book_details .billing-box h3 { margin-bottom: 20px; }
.book_details .billing-box input, .billing input { border-radius: 10px;  border: 1px solid #DADADA;  width: 49%; height: 53px;  margin-bottom: 15px; padding-left: 10px;}
.book_details .billing-box input::placeholder, .billing input::placeholder {font-size: 13px; color: var(--light-grey); font-weight: 400; }
.book_details .billing-box input:focus-visible, .billing input:focus-visible { outline: none; padding-left: 10px;}
.book_details .billing-box .prep, .billing input:nth-child(even) { margin-right: 15px;}
.book_details .ploicy_box{ border-radius: 20px; background: #FFF; box-shadow: 0 4px 44px 0 rgba(0, 0, 0, 0.05); margin-bottom: 30px; margin-top: 26px; padding: 40px;}
.book_details .box22{padding: 29px;}
.book_details .box22 a{ border-radius: 10px; border: 1px solid #D9D9D9;  background: #FFF;  color: var(--light-grey);  font-size: 13px;  font-weight: 400;  padding: 10px;  text-decoration: none;  margin-right: 12px;  }
/* .book_details .confirm { text-align: center; padding: 13px 44px;} */
/* .book_details .confirm .button { padding: 13px 44px;} */
select:not(.selection select) {  -webkit-appearance: none; /* removes expand icon in older Chrome versions */  -moz-appearance: none; /* removes expand icon in older Firefox versions */  appearance: none; /* removes expand icon */  }
.filter_box{display: flex;}
.bookings .filter_box .dropdown{margin-right: 10px;}
.bookings .filter_box .dropdown button{padding: 15px 30px;}
.bookings .filter_box .dropdown button:focus, .bookings .filter_box .dropdown button:focus-visible { outline: none;box-shadow: none;}


/* --------------Inbox Page------------------ */


/* ------------Chat Box-------------- */

.session-sec-1 .chat {  padding: 20px; border-radius: 12px; }
.session-sec-1 .chat .chat_header { padding: 8px 25px; border-radius: 13px;}
.session-sec-1 .chat .chat_header .user_img { height: 60px; width: 60px;}
.session-sec-1 .chat .chat_header .title { flex: .99; display: flex; align-items: center; font-weight: 500;}
.session-sec-1 .chat .chat_header i { margin-left: 10px;}
.session-sec-1 .chat .chat_footer { padding-top: 20px;}
.session-sec-1 .chat .chat_footer .chat_text { border-radius: 114px; background-color: var(--white); padding: 2px 20px; flex: .95; border: 1px solid #E3E5E5;}
.session-sec-1 .chat .chat_footer .chat_text input { border: none; padding: 10px 20px; background-color: transparent; flex: 1;}
.session-sec-1 .chat .chat_footer i, .session-sec-1 .chat .chat_header i { font-size: 20px; color: var(--black);}
.session-sec-1 .chat .chat_footer .icons { background-color: var(--light-yellow); height: 50px; width: 50px; display: flex; align-items: center; justify-content: center; border-radius: 50%; }
.session-sec-1 .chat .chat_footer .icons .icon-microphone { color: var(--white)}
.session-sec-1 .chat .chat_body { height: 70vh; overflow: auto;}
.session-sec-1 .chat .chat_body > div.d-flex { height: 100%;}
.session-sec-1 .chat .chat_body .time { width: 100%; position: relative; margin-bottom: 15px;}
.session-sec-1 .chat .chat_body .time p { background-color: var(--white); width: fit-content; margin: auto; padding: 0 10px; color: #979C9E;}
/* .session-sec-1 .chat .chat_body .time::before { content: ''; position: absolute; border-top: 1px solid grey; width: 100%; top: 10px; left: 0; z-index: -1;} */
.session-sec-1 .chat .chat_body .msg { width: fit-content; margin-bottom: 10px; display: flex; gap: 5px; align-items: end; }
.session-sec-1 .chat .chat_body .msg p { background-color: var(--light-yellow); padding: 15px 20px; border-radius: 33px;}
.session-sec-1 .chat .chat_body .msg .chat_img  { height: 25px; width: 25px; margin-bottom: 1.2rem; }
.session-sec-1 .chat .chat_body .msg_left { margin-right: auto; flex-direction: row-reverse; }
.session-sec-1 .chat .chat_body .msg_left p { background-color: var(--comment-grey);}
.session-sec-1 .chat .chat_body .msg_left.typing p { background-color: transparent; margin-bottom: 7px; padding-left: 2px; color: #979C9E; animation: type 3s infinite 3s ease-in-out; overflow: hidden; transition: .5s ease-in-out;}
/*.session-sec-1 .chat .chat_body .msg_left.typing p span { animation: dots 3s infinite 4s; }*/
.session-sec-1 .chat .chat_header .icons { display: flex; gap: 8px; }
.session-sec-1 .chat .chat_header .icons a.bg_yellow { border-radius: 50%; display: inline-block; height: 40px; width: 40px; padding: 10px 0;}
.session-sec-1 .chat .chat_header .icons a.bg_yellow.drop { padding-inline: 5px;}
.session-sec-1 .chat .chat_header .icons a.bg_yellow.drop + .dropdown-menu.show { transform: translate(-150px, 60px) !important; min-width: 12rem; filter: drop-shadow(0px 0px 4px rgba(0, 0, 0, 0.25)); border-radius: 10px;}
.session-sec-1 .chat .chat_header .icons a.bg_yellow.drop + .dropdown-menu.show a.dropdown-item.fs-18 { font-weight: 400; display: flex; justify-content: space-between; align-items: center;}
.session-sec-1 .chat .chat_header .icons a.bg_yellow.drop + .dropdown-menu.show a.dropdown-item:focus, .session-sec-1 .chat .chat_header .icons .dropdown-item:hover { background-color: var(--light-yellow); }
.session-sec-1 .chat .chat_header .icons ul.dropdown-menu.show { position: relative; padding: 15px 0;}
.session-sec-1 .chat .chat_header .icons ul.dropdown-menu.show::before { content: ''; border-top: 1px solid #baa9a9; border-left: 1px solid #baa9a9; position: absolute; top: -11px; right: 17px; display: block; height: 20px; width: 20px; transform: rotate(45deg); background-color: var(--white);     border-top-left-radius: 6px;}

@keyframes type {
    0% {width: 0; padding-right: 0; transition: 1s linear;}
    50% {width: 45px; transition: 1s linear;}
    70% {width: 65px; transition: 1s linear;}
    85% {width: 75px; transition: 1s linear;}
    100% {width: fit-content; transition: 1s linear;}
}
/*@keyframes dots {*/
    /*from {opacity: 0; transition: 2s linear}*/
    /*to {opacity: 1;}*/
/*}*/


/* ---Chat Sidebar--- */

.chat-sec-1 .chats { margin-bottom: 15px; box-shadow: 0 0 8px rgba(189, 189, 189, 0.10); border-radius: 12px; padding: 15px 10px 6px; }
.chat-sec-1 .chats:hover { background-color: var(--light-yellow);  color: #000;  }
.chat-sec-1 .chats a:hover { color: #000;  }
.chat-sec-1 .chats .img, .chat-sec-1 .chats .img>img { border-radius: 50%; }
.chat-sec-1 .chats .img { height: 65px; width: 65px; margin-bottom: 10px; }
.chat-sec-1 .chats .chats_body { padding-left: 10px; }
.chat-sec-1 .chats .chats_body .body p { max-width: 250px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; color: var(--text_grey); font-weight: 400;}
.chat-sec-1 .heading { padding-block: 10px;}
.chat-sec-1 .action_btn button { padding: 0 5px;}
.chat-sec-1 .search-container { position: unset; width: 100%; padding: 12px 15px; border-radius: 33px; background-color: #FFE998;}
.chat-sec-1 .search-container input { background-color: transparent;  border: none; width: 90%; padding-left: 10px;}
.chat-sec-1 .search-container .fa-search { left: 30px; top: 62%;}
.chat-sec-1 .all_chat { height: 80vh; overflow: auto;}


/*------------Upload Page------------*/

.text-shadow { text-shadow: 0 4px 4px rgba(0, 0, 0, 0.10);}
.sec1_upload { color: var(--light-grey);}
.sec1_upload .required_list { background-color: var(--light-blue);  border-radius: 20px; max-width: 58%; text-align: left; padding-left: 55px !important; margin: 20px auto;}
.sec1_upload .required_list li { position: relative; list-style: none; padding-bottom: 10px;}
.sec1_upload .required_list li:before {  content: "\2713"; font-family: "Font Awesome 5 Free"; font-size: 22px; margin-right: 5px; position: absolute; top: 0; left: -28px; height: 20px; width: 20px;  }


/*-----------Cart Page---------------*/

.sec1_cart { min-height: 50.6vh;}
.sec1_cart .property-image { height: 180px;  width: 180px; position: relative; }
.sec1_cart .delete { position: absolute; top: 8px; left: 8px;}
.sec1_cart .delete i.fa.fa-trash.bg_yellow { padding: 8px; font-size: 12px; border-radius: 50%;  color: #585858; cursor: pointer;}
.sec1_cart .delete i.fa.fa-trash.bg_yellow:hover { transform: scale(1.2);}
.sec1_cart .cart-asset2 p.fs-12 { margin: 0;}
.sec1_cart .button1 { background-color: var(--light-yellow); width: 500px; margin: 30px auto 0;}
.sec1_cart .cart i { font-size: 28px;}
.sec1_cart .cart-asset .listing_desc { height: 42px; margin-bottom: 15px;}
.sec1_cart .cart-asset .listing_desc p { margin: 0; font-size: 14px; }
.sec1_cart .cart-asset .listing_desc p::first-letter { text-transform: capitalize; }

/* -----Stepper Form To Upload ID----------- */

.sec1_uploadID fieldset:not(:first-of-type) { display: none  }
.sec1_uploadID .file { opacity: 0; height: 100%; width: 100%;}
.sec1_uploadID .files { height: 250px; width: 100%;  position: relative;}
.sec1_uploadID .files:before, .sec1_uploadID .radio .form-outline input[type="radio"]::after, .sec1_uploadID .radio .form-outline input[type='radio']:checked::after { content: ''; position: absolute;  top: 0; left: 0;}
.sec1_uploadID .files:before  {  background-image: url(../images/default.png); background-repeat: no-repeat; background-size: cover;  max-width: 160px; height: 140px; bottom: 0; right: 0; margin: auto; z-index: -1;}
.sec1_uploadID input.nextt, .sec1_uploadID input.nexttt { width: 100%; background-color: var(--light-yellow); margin: 0 auto;}
.sec1_uploadID input.nextt:not(fieldset:first-child .nextt) { width: 70%;}
.sec1_uploadID p.warning_info { background-color: var(--light-blue); padding: 10px; border-radius: 12px;}
.sec1_uploadID .radio .form-outline label { padding-right: 20px; color: var(--light-grey) ; font-size: 18px; font-weight: 500; }
/*.sec1_uploadID .radio .form-outline {background-color: var(--light-yellow); padding-block: 15px; width: 260px; border-radius: 33px;}*/
.sec1_uploadID .radio .form-outline label {  padding-block: 15px; width: 260px; border-radius: 33px; box-shadow: 0 0 8px 0 rgba(189, 189, 189, 0.25);}


/* .sec1_uploadID .radio .form-outline input[type='radio'] { position: relative;  height: 35px; width: 35px; margin: 10px 0 0 10px;  }
.sec1_uploadID .radio .form-outline input[type="radio"]::after { width: 100%;  height: 100%;  border-radius: 50%;  border: 3px solid var(--light-yellow);  }
.sec1_uploadID .radio .form-outline input[type='radio']:checked::after { width: 100%;  height: 100%;  border-radius: 50%;  background-color: var(--white); border: 2px solid var(--light-yellow); box-shadow: 0px 0px 0px 3px var(--white); } */

.sec1_uploadID .radio .form-outline input[type='radio'] { opacity: 0;}
.sec1_uploadID .radio .form-outline input[type='radio'] + label {position: relative; margin: 10px 0 0 10px;}
.sec1_uploadID .radio .form-outline input[type="radio"] + label::after, .sec1_uploadID .radio .form-outline input[type='radio']:checked  + label::after { content: ''; display: inline-block; width: 35px; height: 35px; position: absolute; bottom: 10px; right: 15px; border-radius: 50%; }
.sec1_uploadID .radio .form-outline input[type="radio"] + label::after { border: 3px solid var(--light-yellow); }
.sec1_uploadID .radio .form-outline input[type='radio']:checked  + label::after { border: 2px solid var(--light-yellow); background-color: var(--white); box-shadow: 0 0 0 3px var(--white);}
.sec1_uploadID .radio .form-outline input[type='radio']:checked  + label { background-color: var(--light-yellow); }


.sec1_uploadID .selection {  max-width: 300px;  margin: auto;  position: relative; padding: 2px 20px;  border: 1px solid #DBDBDB;  border-radius: 33px; width: 300px;  }
.sec1_uploadID .selection select {  padding: 20px 0;  border: none; width: 100%; outline: 0; }
.sec1_uploadID .selection p {  position: absolute;  top: -55px;  left: 0;  bottom: 0;  right: 115px;  margin: auto;  background: #fff;  height: 30px;  width: 145px;  }
.sec1_uploadID .selection option:hover { background-color: var(--light-yellow) !important;}


/*-----------Checkout Page------------*/

.sec1_checkout .main_cart {gap: unset;}
.sec1_checkout .property-image { height: unset; width: unset; }
.sec2_checkout input { margin-bottom: 25px; padding: 10px 20px;}
.sec2_checkout input[type="number"]:not(input.card_number) { width: 48%; margin-right: 10px;}
.sec2_checkout .sec2_checkout select { border-radius: 22px;; padding: 10px 20px; cursor: pointer;}
.sec2_checkout .input-group-text { background-color: #fff; border-right: none; cursor: pointer; padding: 16px; border-radius: 10px;}
.sec2_checkout .table td { font-size: 16px;}
.sec2_checkout .table th { font-size: 25px; font-weight: 500;}

.star-rate{  font-family: 'Poppins'; font-size: 14px; font-weight: 400;  line-height: 25px; }
.user_info {display: flex;flex-direction: column;padding-top: 5px;}
.wish-heart:before{    content: '';background: url(../images/heart.png);height: 50px;width: 25px;display: flex;object-fit: contain;background-size: contain;background-position: center;background-repeat: no-repeat;}
.user_info .div-review { align-items: center;  display: flex;  gap: 5px;  font-family: Poppins;  font-size: 12px;  font-weight: 400;  line-height: 18px;  letter-spacing: 0em;  text-align: left;  }
.user_info .div-review i.fas.fa-star {font-size: 10px;}
.sec1_detail .info h5.py-3 span.rating {    display: flex;align-items: center;gap: 5px;}
.sec2_detail .fa-user:before {content: ''; background-image: url(../images/man.png); height: 20px; width: 21px; display: flex; background-size: cover; object-fit: contain; background-repeat: no-repeat;}
.sec2_detail .info-border {border-top: 1px solid var(--grey);}

input.checkbox { accent-color: var(--light-yellow); height: 15px; width: 15px; position: relative;}
input.checkbox:before { content: ''; position: absolute; top: 0px; left: -1px; bottom: 0; right: 0; z-index: 0; border: 2px solid black; border-radius: 5px; height: 17px; width: 17px; margin: auto; opacity: 0;  }
input.checkbox:checked:before { opacity: 1;}
input[type=radio] { accent-color: var(--light-yellow);  }
.product_box p:not(.star-rate) { display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; height: 48px;}
.product_box p:not(.star-rate):empty { display: none;}
.sec1_detail .slide_img { height: 40em; width: 100%; }
.sec1_detail .slide_img img, .slides_img img { height: 100%; width: 100%; object-fit: cover; }
.sec1_detail .slides_img { height: 9em; }

.modal_btn, .modal_btn:hover { background: transparent; border: none;}
.experience .info { box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15); border-radius: 12px;}
.vertical_divider { border-left: 1px solid #BDBDBD; }

.detail_info p, .cart-asset .listing_desc { display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; word-break: break-all;}
.sec1_detail .btn_info label { height: 32px; width: 32px; cursor: pointer;}
.sec1_detail .btn_info label svg { height: 100%; width: 100%; object-fit: cover;}

.sec2_detail .inner_detail p { display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; margin: 0; padding: 0 !important;     height: 50px; transition: 0.3s ease-in-out;}
.sec2_detail .inner_detail.show p { display: block; height: fit-content; overflow: unset; transition: 0.3s ease-in-out;}
.sec2_detail .read-more { margin-top: 15px;}

button.btn.btn-link, button.btn.btn-link:active, button.btn.btn-link:focus { outline: none; box-shadow: none;}
.book_img img { border-radius: 20px;}
button.transparent.button { color: var(--blue);}


/* ----Modal Calendar----- */

.modal.calen .modal-content {  background-color: transparent;  border: none; max-width: 400px; }
.modal.calen .cal .pignose-calendar { width: 100%;}
.modal.calen .modal-body  { padding-bottom: 0;}
.modal.calen .modal-footer  { padding: 0; margin-inline: 16px;}
.modal.calen .modal-footer button { width: 50%; margin: 0; padding: 10px; border: none;}
.modal.calen .modal-footer button.cancel { background-color: #fff; }
.modal.calen .modal-footer button.save { background-color: var(--btn-yellow); }
.modal .modal-content { padding: 0 20px 15px; border-radius: 20px;}
.modal .modal-content .divider:last-child { border: 0;}
.modal .btn-close { position: absolute; right: 18px; top: 20px;}

i.eye_icon { position: absolute; top: 14px; right: 14px; cursor: pointer;}
.modal .form-outline { position: relative;}

.info_clock { position: absolute; top: 30px; right: 8px;}
.time_picker { border: 0;}
.time_picker[disabled] + i { color: #a99c9c;}

/* Privacy and terms */

.privacy h1, .privacy h2, .privacy h3, .privacy h4, .privacy h4, .privacy h6 { font-size: 24px; font-weight: 600; padding: 18px 0; word-wrap: break-word;}

.main_cart a:hover { color: #000;}
.main_cart .cart-asset p:empty { display: none;}


/* FAQ Page */

.faq_sec { margin: 10px; box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.05); border-radius: 20px; background: #FFF; padding: 20px;  }

.accordion-item:first-of-type .accordion-button, .accordion-item:first-of-type, .accordion-item:last-of-type, .accordion-item:last-of-type .accordion-button.collapsed { border-radius: 10px;}

.accordion-item { margin: 15px 0; border: none; border-radius: 33px; padding: 0 20px; font-size: 15px;}
.accordion-header, .accordion-button, .accordion-button:focus {  border-radius: 10px; font-size: 16px; font-weight: 600;}
.accordion-button, .accordion-button:focus { border: 1px solid #DADADA; }

.accordion-body { border-radius: 15px;}

.accordion-button:not(.collapsed) { color: black; background-color: #fff; box-shadow: none; border-radius: 15px;}
.accordion-button:focus { box-shadow: none;}
.accordion-collapse, .collapse.show, .accordion-item:last-of-type .accordion-collapse { border: 1px solid #DADADA; border-radius: 0 0 10px 10px; border-top: none;}
.accordion-button[aria-expanded="true"]  { border-bottom: 0; border-radius: 10px 10px 0 0 !important;}


/* Report modal */

.parent_flag { right: 12px; top: 8px; z-index: 9;}

.parent_flag .flag, .parent_flag .cancel { background-color: var(--dark-yellow); display: inline-block; height: 25px; width: 25px; text-align: center; border-radius: 50%; font-size: 12px; cursor: pointer;  padding: 3px;}
.parent_flag .cancel { color: var(--dark-yellow); background-color: #000; padding: 2px;}
.parent_flag i { font-size: 10px;}


/* Alerts */
body div:where(.swal2-container) * { font-family: 'Poppins';}
body div:where(.swal2-container) div:where(.swal2-popup) { border-radius: 22px;}
body div:where(.swal2-icon).swal2-warning, div:where(.swal2-icon).swal2-success .swal2-success-ring{  border-color: var(--btn-yellow) !important; color: var(--btn-yellow) !important; background-color: var(--btn-yellow) !important;}
body div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm { background-color: var(--btn-yellow); color: #000; border-radius: 7px; padding: 13px 40px 13px 40px;}
body div:where(.swal2-container) button:where(.swal2-styled).swal2-cancel { background-color: #000 !important; color: #fff;  border-radius: 7px; padding: 13px 40px 13px 40px ;}
body div:where(.swal2-container) button:where(.swal2-styled).swal2-default-outline:focus, div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm:focus { box-shadow: none !important;}
body div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm { background-color: var(--btn-yellow); font-family: 'Poppins';}
.swal2-actions button { border-radius: 7px; font-family: 'Poppins'; }
body div:where(.swal2-icon).swal2-success [class^=swal2-success-line] { background-color: #fff !important; z-index: 4 !important;}
body div:where(.swal2-icon).swal2-error { background-color: var(--btn-yellow); border-color: var(--btn-yellow);}
body div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line] { background-color: #fff;}
body div:where(.swal2-container) div:where(.swal2-html-container) { font-size: 14px;}
body div:where(.swal2-container) h2:where(.swal2-title) { font-size: 22px;;}

.faq .search_parent { background-color: #fff; box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.09); border-radius: 22px; /* padding-block: 12px; */ margin-inline: 15px;}

.ui-widget.ui-widget-content { z-index: 20 !important; border-radius: 10px;}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default, .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active { background-color: #fff !important; border-radius: 5px !important; border: 0 !important; text-align: center;}
.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight { background-color: var(--btn-yellow) !important; color: #fff !important; border: 0 !important; }

.ui-state-default:hover, .ui-widget-content .ui-state-default:hover { background-color: var(--btn-yellow) !important; color: #fff !important;}


/* Filter */
.adv_filter_menu  { background-color: #FFFFFFA8; padding: 20px 16px 20px 16px; border-radius: 20px; width: 400px; backdrop-filter: blur(5px);}
.adv_filter_menu .bg_white { padding: 20px; border-radius: 12px; box-shadow: 0px 0px 4px 0px #00000040; margin-bottom: 15px;}
.adv_filter_menu select,  .select2, .adv_filter_menu input { padding: 5px 10px; border: 2px solid #FFD324; border-radius: 33px;    display: block; width: 100%;}
.adv_filter_menu select:focus { box-shadow: none; border: 2px solid #FFD324;}
.adv_filter_menu .selection, .adv_filter_menu .select2-container--default.select2-container--focus .select2-selection--multiple { border: 0;}
.adv_filter_menu .select2 { display: block; width: 100%;}
.adv_filter_menu .select2-selection__rendered { background-color: transparent;}
.adv_filter_menu .select2-container--default .select2-selection--multiple { border: 0; }
.adv_filter_menu .button { border: 0; width: 48%; margin: 0 auto; }
.adv_filter_menu .yellow_btn { background-color: var(--btn-yellow); }
.adv_filter_menu .submit_btn  .button:hover { background-color: var(--btn-yellow) !important; color: #000 !important; }
.adv_filter_menu .submit_btn .yellow_btn:hover { background-color: #fff !important; color: #000 !important;  }
/* .adv_filter_menu .select2-container--default.select2-container--focus .select2-selection--multiple:before, .adv_filter_menu .select2-container--default .select2-selection--multiple:before { content: 'Select Amenities'; padding: 10px; margin: 0 auto;} */

/* .adv_filter_menu .select2-container .select2-selection--multiple .select2-selection__rendered { position: relative; top: 30px; height: 85px; overflow: scroll;}
.adv_filter_menu .amenity { min-height: 200px;} */
.adv_filter_menu .select2-container--default .select2-selection--multiple .select2-selection__choice { background-color: transparent; border: 0; box-shadow: 0px 0px 4px 0px #00000040; padding: 2px 18px; border-radius: 8px;}

/* Select 2 */
.select2-container--default .select2-results>.select2-results__options, .select2-container--open .select2-dropdown--below { border-radius: 12px; border: 0; padding: 20px 12px; background-color: #fff; box-shadow: 0px 0px 4px -1px #00000040; right: 0; left: 0;}
.select2-container--default .select2-results>.select2-results__options { box-shadow: none;}
.select2-results__option[aria-selected] { margin: 10px 0;}
.select2-container .select2-container--default .select2-results__option--highlighted[aria-selected] { background-color: transparent;}
body .select2-container .select2-container--default .select2-results__option[aria-selected=true] { background-color: var(--btn-yellow); color: #000;}
/* .select2-container .select2-container--default .select2-results__option[aria-selected=true], .select2-container--default .select2-results__option[aria-selected=true] {appearance: none;-webkit-appearance: none;-moz-appearance: none;background: transparent;background-image: url('../img/circle-dot.png');background-repeat: no-repeat;background-position-x: 97%;background-position-y: 13px;} */
.select2-container .select2-container--open .select2-dropdown { left: auto; right: 0;}
.select2-results { border-radius: 12px;}
.select2-container--default .select2-results>.select2-results__options { padding: 0;}
.select2-dropdown {z-index: 1061 !important;}
.select2-container--default.select2-container--focus .select2-selection--multiple {border: 0;}
.select2-container--default .select2-selection--multiple{border: 0;}


/* Range Selector */
.irs--round .irs-bar { background-color: var(--btn-yellow);}
.irs--round .irs-handle { background-color: #000; border-color: #000;}
.irs--round .irs-handle {width: 16px;height: 16px; top: 29px}
.irs--round .irs-from, .irs--round .irs-to, .irs--round .irs-single { background-color: transparent; color: #666666;}
.irs--round .irs-from:before, .irs--round .irs-to:before, .irs--round .irs-single:before,.irs--round .irs-min, .irs--round .irs-max { display: none;}

/* Filters Modals CSS */
.Residence-modal .resident-list .icheck-list, .rules-list .icheck-list{display: flex;justify-content: flex-start;align-items: center;padding-left: 0;gap: 21px;flex-wrap: wrap;}
.Residence-modal .resident-list .icheck-list li, .rules-list .icheck-list li{list-style: none;display: flex;justify-content: flex-start;align-items: center;border-radius: 12px;border: 1px solid #E9E9E9;padding: 10px 15px;gap:10px;}
.Residence-modal .resident-list .icheck-list li label, .rules-list .icheck-list {color: #202020;font-family: Poppins;font-size: 14px;font-style: normal;font-weight: 400;line-height: normal;text-transform: capitalize;}
.Residence-modal .modal-body .bg_white .title{color: #4D4D4D;font-family: Poppins;font-size: 16px;font-style: normal;font-weight: 500;line-height: normal;text-transform: capitalize;margin-bottom: 15px;}
.Residence-modal .modal-body .bg_white {padding-top: 20px;border-bottom: 1px solid #DBDBDB;padding-bottom: 15px;}
.Residence-modal .modal-body .bg_white:last-child { border: 0; }
.Residence-modal .modal-body .custom-radio>input{display:none;}
.Residence-modal .modal-body .custom-radio>input:checked+label {background: #FFD324;border: 1px solid #FFD324;color: #4D4D4D;}
.Residence-modal .modal-body .custom-radio > label {color: #202020 !important;font-family: Poppins !important;font-size: 14px !important;font-style: normal !important;font-weight: 400 !important;
line-height: normal !important;text-transform: capitalize;padding: 10px 33px !important;border-radius: 16px;border: 1px solid #BDBDBD;margin-inline: 5px;margin-bottom: 15px;}
.Residence-modal .modal-footer .yellow-btn{color: #4A4A4A;font-family: Poppins;font-size: 16px;font-style: normal;font-weight: 400;
line-height: normal;text-transform: capitalize;border-radius: 33px;background: #FFCE32;border: 1.5px solid #FFCE32;padding: 10px 45px;}
.Residence-modal .modal-footer .white-btn{color: #4A4A4A;font-family: Poppins;font-size: 16px;font-style: normal;font-weight: 400;line-height: normal;
text-transform: capitalize;border-radius: 33px;border: 1.5px solid #FFCE32;background: #FFF;padding: 10px 45px;}
.Residence-modal .modal-footer{justify-content: center !important;}
.Residence-modal .modal-header{border-bottom: 0;padding-bottom: 0;justify-content: center;}
.Residence-modal .modal-dialog{max-width: 650px;}
.Residence-modal .modal-body { padding: 2rem;padding-top: 1rem; }
.Residence-modal .modal-body .price-list ul{padding-left: 0;display: flex;justify-content: flex-end;align-items: center;gap: 15px}
.Residence-modal .modal-body .price-list ul li{list-style: none;}
.Residence-modal .modal-body .price-list{display: flex;justify-content: space-between;align-items: center;}

.Residence-modal .modal-body { max-height: 70vh; overflow: hidden; overflow-y: scroll; }
body:has(.Residence-modal .modal.show) { overflow: hidden; padding: 0 !important;}
.Residence-modal .modal-body .day_time { display: none; }

.Residence-modal .modal-body .rooms_parent { width: 35%; }

/* Details pages Css */
/* .residence-sec .amenities-box,.residence-sec .key_features, .residence-sec .detail-rw{padding: 40px 0px;border-bottom: 1px solid #E6E6E6;} */
.residence-sec .amenities-box h3{color: #4A4A4A;font-family: Poppins;font-size: 22px;font-style: normal;font-weight: 500;line-height: normal;}
.residence-sec .amenities-box .parent-box{display: flex;justify-content: flex-start;align-items: center;gap: 4em;}
.residence-sec .amenities-box .parent-box .box{border-radius: 12px;border: 1px solid #E9E9E9;padding: 10px 20px;}
.residence-sec .amenities-box .parent-box .box span{color: #4A4A4A;font-family: Poppins;font-size: 16px;font-style: normal;font-weight: 400;line-height: normal;text-transform: capitalize;}
.residence-sec .key_features h3{color: #4A4A4A;font-family: Poppins;font-size: 22px;font-style: normal;font-weight: 500;line-height: normal;text-transform: capitalize;}
.residence-sec .key_features .parent-feature{display: flex; gap: 25px;align-items: stretch; flex-wrap: wrap;}
.residence-sec .key_features .parent-feature .box{border-radius: 12px;border: 1px solid #E9E9E9;background: #FFF;padding: 14px 20px;padding-bottom: 0; max-width: 333px; flex: 1;}
.residence-sec .key_features .parent-feature .box h6{color: #4D4D4D;font-family: Poppins;font-size: 15px;font-style: normal;font-weight: 600;line-height: 150%;text-transform: capitalize;}
.residence-sec .key_features .parent-feature .box p{color: #4D4D4D;font-family: Poppins;font-size: 12px;font-style: normal;font-weight: 400;line-height: 150%;text-transform: capitalize;}
.residence-sec .details-box .box{display: flex;justify-content: flex-start;align-items: baseline;gap: 15px;margin-bottom: 20px;}
.residence-sec .details-box .box h6{color: #4A4A4A;font-family: Poppins;font-size: 22px;font-style: normal;font-weight: 500;line-height: normal;text-transform: capitalize;}
.residence-sec .details-box .box ul{padding-left: 1rem;}
.residence-sec .details-box .box ul li{color: #5A5A5A;font-family: Poppins;font-size: 18px;font-style: normal;font-weight: 400;line-height: normal;text-transform: capitalize;}
.map-btn{color: #4D4D4D;font-family: Poppins;font-size: 12.853px;font-style: normal;font-weight: 400;line-height: normal;
text-transform: capitalize;border-radius: 66px;border: 1px solid #4D4D4D;background: #FFD324;box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);}
.map-btn > img{margin-left: 5px;}

.topbar #search-box #adv_filter { padding: 0; height: 40px; width: 40px; background-color: #000;}
/* .topbar #search-box .top_categories_wrapper .box:has(input[type="radio"]:checked) .radio_img {filter: brightness(0) saturate(100%) invert(95%) sepia(35%) saturate(6325%) hue-rotate(329deg) brightness(98%) contrast(105%);} */
.topbar #search-box .top_categories_wrapper .box:has(input[type="radio"]:checked) .radio_img {filter: brightness(0) saturate(100%) invert(0%) sepia(100%) saturate(16%) hue-rotate(246deg) brightness(98%) contrast(105%);}
.topbar #search-box .top_categories_wrapper .box .find_btn {color: #4D4D4D; font-size: 14px;}
.topbar .filter_btn_wrapper.hidden {display: none !important;}
.show_map_sectiom.hidden {display: none;}


/* For CDNS */
.swiper-button-next::after {content: '';}
.swiper-button-prev::after {content: '';}

div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content { color: #fff;}


/* Help Center Articles */

.help_center .view_btn_parent .view_btn { background-color: #fff; padding: 6px 20px; border-radius: 6px; border: 2px solid var(--btn-yellow); transition: 0.3s ease-in; }
.help_center .view_btn_parent .view_btn[disabled], .view_btn_parent .view_btn:hover { background-color: var(--btn-yellow); transition: 0.3s ease-in; }
.help_center .view_btn_parent .view_btn[disabled] { cursor: no-drop; }

.help_center .box_parent .box-view-item .card { border: 0; align-items: center; border-radius: 12px; text-align: center; cursor: pointer; padding: 30px 20px 40px;  transition: 0.3s ease-in; /* box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px; */}
.help_center .box_parent .box-view-item .card .card-title:first-letter { text-transform: capitalize;}
.help_center .box_parent.list-view .box-view-item .card  { align-items: start; text-align: start; padding: 8px 15px;}
.help_center .box_parent.box-view .box-view-item .card  { height: calc(100% - 40px); }
.help_center .box_parent .box-view-item .card:hover { box-shadow: 0 0 8px 0 rgb(222 180 27 / 25%); transform: scale(0.98); transition: 0.3s ease-in;}

/* Help Center Detail Page */
.help_center.detail_sec .box_parent .box_category .card, .help_center.detail_sec .faq_parent .card.faq_card  { border: 0; border-radius: 12px; padding: 12px;  transition: 0.3s ease-in; }
.help_center.detail_sec .faq_parent .card.faq_card { margin-bottom: 20px; padding-block: 20px; }
.help_center.detail_sec .faq_parent .card.faq_card:hover{ color: var(--btn-yellow);  box-shadow: 0 0 8px 0 rgb(222 180 27 / 25%);  transition: 0.3s ease-in; }

/* Breadcrumb */

.breadcrumb a { text-decoration: none; transition: 0.3s ease-in;}
.breadcrumb a:hover { color: #000; transition: 0.3s ease-in;}
.breadcrumb a:after { content: ''; display: block; width: 0; height: 1px;background-color: var(--btn-yellow); margin-top: 3px; transition: 0.2s ease-in; }
.breadcrumb a:hover:after { width: 100%; transition: 0.2s ease-in; }


/* Listing Detail page style starts from here */

/* Box style */
.sec-2-detail .listing_data .parent-box .box { border-radius: 12px; border: 1px solid var(--comment-grey); padding: 10px 20px; min-height: 45px; font-size: 12px;}
.sec-2-detail .listing_data .parent-box.parent-feature .box { flex-grow: 1; word-break: break-word; }
.sec-2-detail .listing_data .parent-box.parent-feature .box p { margin: 0; }
.sec-2-detail .listing_data:has(.listing_data_heading) .parent-box { padding-top: 15px;}
.sec-2-detail .listing_data .parent-box .box span:first-letter {  text-transform: capitalize;}

.sec-2-detail .listing_data.listing_amenities .parent-box, .modal.all-amenties .parent-box { gap: 15px;}
.sec-2-detail .listing_data.listing_amenities .parent-box .box { width: calc(100% - 50px); border: 0; }
.sec-2-detail .listing_data ol.parent-box  { list-style-position: inside; }
.sec2_detail .button.reserve:not([disabled]) { background-color: var(--btn-yellow); border-color: var(--btn-yellow);}

.listing_amenities .amenity-data { font-size: 15px;}
.listing_amenities .amenity-data .amenties_desc { font-size: 12px; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;}

/* All amenties Modal */
.modal.all-amenties .modal-body { max-height: 70vh; overflow: scroll;}

/* Feature Box style */
/* .sec-2-detail .listing_data .parent-box.parent-feature .box { max-width: 330px; flex: 1;} */


.cancelation_policy_timeline_wrapper, .cancelation_policy_timeline_wrapper .timeline_step { margin: 0; padding: 0; color: #000; font-family: verdana; font-size: 14px;}
.cancelation_policy_timeline_wrapper { display: flex; max-width: 800px; width: 96%; justify-content: space-between; position: relative; padding: 70px 0; margin-left: 10px;}
.cancelation_policy_timeline_wrapper:after { content: ''; display: block; width: 100%; height: 3px; background-color: #000; position: absolute; top: 50%; left: 0; transform: translate(0, -50%); z-index: -1; }
.cancelation_policy_timeline_wrapper .timeline_step { list-style-type: none; position: relative; cursor: pointer; }
.cancelation_policy_timeline_wrapper .timeline_step:after { content: ''; display: block; width: 18px; height: 18px; border-radius: 50%; border: 2px solid #000; }
.cancelation_policy_timeline_wrapper .timeline_step .refund_date { position: absolute; top: calc(100% + 20px); left: 50%; transform: translate(-50%, 0); display: inline-block; text-align: center; width: calc(800px / 8); }
.cancelation_policy_timeline_wrapper .timeline_step:first-child:after {background-color: #000;}
.cancelation_policy_timeline_wrapper .timeline_step:after {background-color: #fff;}
.cancelation_policy_timeline_wrapper .timeline_step:last-child:after {background-color: #fff; border: 2px solid #929292;}
.cancelation_policy_timeline_wrapper:has(.timeline_step:nth-child(4)) .timeline_step:first-child:before {content:"Full Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px / 3);}
[lang="es"] .cancelation_policy_timeline_wrapper:has(.timeline_step:nth-child(4)) .timeline_step:first-child:before {content:"Reembolso Completo";}
.cancelation_policy_timeline_wrapper:has(.timeline_step:nth-child(4)) .timeline_step:nth-child(2):before {content:"Partial Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px / 3);}
[lang="es"] .cancelation_policy_timeline_wrapper:has(.timeline_step:nth-child(4)) .timeline_step:nth-child(2):before {content:"Reembolso parcial";}
.cancelation_policy_timeline_wrapper:has(.timeline_step:nth-child(4)) .timeline_step:nth-child(3):before {content:"No Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px / 3);}
[lang="es"] .cancelation_policy_timeline_wrapper:has(.timeline_step:nth-child(4)) .timeline_step:nth-child(3):before {content:"Sin reembolso";}
.cancelation_policy_timeline_wrapper:not(:has(.timeline_step:nth-child(4))) .timeline_step:first-child:before {content:"Partial Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px / 2);}
.cancelation_policy_timeline_wrapper:not(:has(.timeline_step:nth-child(4))) .timeline_step.flexible_full_refund:first-child:before {content: "Full Refund";}
 [lang="es"] .cancelation_policy_timeline_wrapper:not(:has(.timeline_step:nth-child(4))) .timeline_step.flexible_full_refund:first-child:before {content: "Reembolso completo";}
.cancelation_policy_timeline_wrapper:not(:has(.timeline_step:nth-child(4))) .timeline_step:nth-child(2):before {content:"No Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px / 2);}
[lang="es"] .cancelation_policy_timeline_wrapper:not(:has(.timeline_step:nth-child(3))) .timeline_step:first-child:before{content: "Sin reembolso";}
.cancelation_policy_timeline_wrapper:not(:has(.timeline_step:nth-child(3))) .timeline_step:first-child:before {content:"No Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px);}
.cancelation_policy_timeline_wrapper:not(:has(.timeline_step:nth-child(3))) .timeline_step:nth-child(2):before {content: ""}
.listing_cancelation .timeline_detailed_wrapper { max-width: 820px; width: 96%; margin-top: 20px;}
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single:not(:last-child) { border-bottom: 1px solid lightgray; margin-bottom: 30px;}
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single { display: flex; align-items: flex-start; padding-bottom: 15px;}
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .timeline_date { flex: 2; }
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .description { flex: 12; }
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .timeline_date .date_tag h5, .listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .description h5 { font-weight: 600; font-size: 14px;}
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .timeline_date .date_tag span, .listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .description p { font-size: 12px; margin-bottom: 0;}

.listing_cancelation .details-box .icon {width: 21px; height: 21px; display: flex; align-items: center;}
.listing_cancelation .details-box .icon img { width: 100%; height: 100%;}

body .divider.listing_data.listing_cancelation {padding: 30px 0 10px 0;}

.listing_cancelation .details-box .box {flex-wrap: nowrap;}

.listing_gallery .slide_img {height: 620px; border-radius: 20px; overflow: hidden;}
.listing_gallery .slide_img img {height: 100%;}

.listing_meta .rating *:not(:last-child) {flex-shrink: 0;}
.listing_meta .report-listing img {width: 13px; height: auto; object-fit: contain; object-position: center;}
.listing_meta .report-listing {text-decoration: none;}
.listing_meta .report-listing span {font-size: 14px; text-decoration: underline; color: #5e5e5e;}

/* .listing_map #map_location .gmnoprint:not(:has(.gm-control-active)), .listing_map #map_location .gm-fullscreen-control {display: none;} */
/* .listing_map #map_location .gm-style-cc, .listing_map #map_location .gm-svpc {display: none;} */
.listing_map #map_location .gm-svpc {border-radius: 10px !important;}
.listing_map #map_location .gm-style-cc {display: none;}
.listing_map #map_location a:has(img[alt="Google"]) {display: none !important;}
.gm-bundled-control .gmnoprint:has([class*="gm-control"]) > div {border-radius: 10px !important;}

.listing_rule .box img {width: 20px; height: 20px; object-fit: contain; object-position: center;}

.sec1_detail .user_img { height: 70px; width: 70px; overflow: hidden; border-radius: 50%;}
.sec1_detail .user_img img { height: 100%; width: 100%; object-fit: cover;}

.listing_map #map_location .gm-style-mtc-bbw .gm-style-mtc:first-of-type>button { border-radius: 10px 0 0 10px;}
.listing_map #map_location .gm-style-mtc-bbw .gm-style-mtc:last-of-type>button { border-radius: 0 10px 10px 0;}
.listing_map #map_location button.gm-control-active.gm-fullscreen-control, .listing_map #map_location .gmnoprint button[aria-label="Zoom in"], .listing_map #map_location .gmnoprint button[aria-label="Zoom out"], .listing_map #map_location .gmnoprint button.gm-control-active { border-radius: 10px !important;}

/* Listing Detail page style ends here */


/* Booking detail */
.booking_detail .book_box p.card-desc { display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;}
.booking_detail .book_box .price_box p { margin-bottom: 5px;}

.booking_detail .pay_box .payment-parent label { border: 2px solid transparent; color: #fff; background-color: #0F52BA; height: 50px; border-radius: 10px; padding: 0 10px; width: 100%; margin-bottom: 10px; cursor: pointer; transition: 0.3s ease-in;}
.booking_detail .pay_box .payment-parent label.paypal { background-color: var(--btn-yellow);}
.booking_detail .pay_box .payment-parent label span.icon { opacity: 0; }
.booking_detail .pay_box .payment-parent input[type="radio"]:hover + label { opacity: 0.85; transition: 0.2s ease-in; }
.booking_detail .pay_box .payment-parent input[type="radio"]:checked + label span.icon { opacity: 1; transition: 0.2s ease-in; right: 10px;}
.booking_detail .pay_box .custom-cards { height: 0; overflow: hidden; opacity: 0; position: relative; z-index: -1; transition: 0.3s ease-in; margin-top: 10px;}
.booking_detail .pay_box:has(#card:checked) .custom-cards.register-card, .booking_detail .pay_box .custom-cards.add-card.active { height: 100%; opacity: 1; z-index: 1; transition: 0.3s ease-in;}
.booking_detail .pay_box:has(#paypal:checked) .custom-cards.paypal-card { height: 100%; opacity: 1; z-index: 1; transition: 0.3s ease-in;}

.booking_detail .pay_box .custom-cards label { padding-bottom: 10px;}

/* .booking_detail .pay_box .add-card-btn {  border-radius: 30px; background-color: #fff; border: 2px solid #000;font-weight: 500;} */

.booking_detail .pay_box .custom-cards .card-img-parent { margin-bottom: 10px;}
.booking_detail .pay_box .custom-cards .card-img-parent .cards-img { /* aspect-ratio: 5 / 3; */ width: 55px; /* object-fit: cover; */ overflow: hidden;}
.booking_detail .pay_box .custom-cards .card-img-parent .cards-img img { height: 100%; width: 100%; object-fit: contain;}

.booking_detail .sec-2-detail .listing_data:last-of-type { border: 0; }

.booking_detail .sec-2-detail p {word-break: break-word;}
.booking_detail .sec-2-detail p:empty { display: none;}

.categories.parent-box { row-gap: 20px; margin-bottom: 10px; flex-wrap: wrap;}
.categories.parent-box li { list-style: none;}

.pull-right { float: right; padding-top: 4px;}
.spinner-border { height: 1.25rem; width: 1.25rem; }
a:has(.download-text.d-none) { pointer-events: none; opacity: 0.55;}


/* Book Box */
.book .select2-container--default.select2-container--focus .select2-selection--multiple, .book .select2-container--default .select2-selection--multiple {  border: 0;  padding: 0;}
/* .book .select2-container--default .select2-selection--multiple .select2-selection__rendered {  display: flex; overflow-x: scroll;} */
.book .select2 {  border: 0;  padding: 0;  min-width: 250px;}
.book .select2 .select2-selection__rendered:after { content: ''; display: inline-block; width: 7px; height: 7px; position: absolute; top: 0; bottom: 0; margin: auto; right: 20px; border: 2px solid #000; border-width: 0 2px 2px 0; rotate: 45deg; }
.book .select2-selection__rendered li:not(:last-child) { display: none;}

.book .select2-container--open .select2-dropdown--below { border-radius: 8px;}
body .select2-container--default .select2-results__option { color: #000; border-radius: 5px; padding-inline: 15px; margin-right: 5px;}
body .select2-container--default .select2-results__option--highlighted[aria-selected]  { background-color: var(--grey); color: #000; }
body .select2-container--default .select2-results__option--highlighted[aria-selected="true"], body .select2-container--default .select2-results__option[aria-selected=true] { background-color: var(--btn-yellow); color: #000; border-radius: 5px;  padding-inline: 15px;}

.book .info .guest_wrapper { margin-top: 10px;}
.book .info .guest_wrapper > button, .guest_wrapper > button, .rooms_parent .guest_wrapper > button, .guest_wrapper > button { width: 35px; height: 35px; border: 1px solid #DADADA; border-radius: 50%; background-color: #fff;}
.book .info .guest_wrapper > button[disabled], .guest_wrapper > button[disabled] { opacity: 0.5; cursor: no-drop;}
.book .info .guest_wrapper > button i, .guest_wrapper > button i { font-size: 12px;}
.book .info .guest_wrapper input { width: 60px;}

/* Confirm Booking */

.guest_wrapper input { width: 25px; transition: 0.3s ease-in; }
.guest_wrapper input[readonly] { background-color: transparent;  transition: 0.3s ease-in; }
.guest_wrapper .btn { transition: 0.3s ease-in; overflow: hidden; width: 100%;}
.trip-detail .guest_wrapper .btn { width: 30px;}
.guest_wrapper .btn.d_none { width: 0; border-width: 0; transition: 0.3s ease-in;}

.booking_detail .confirm-btn[disabled] { cursor: no-drop; background-color: transparent; border: 1px solid var(--light-grey); transition: 0.3s ease-in;}

.booking_detail .confirm_booking_card .col_left .swiper-slide img {height: 300px;}


/* My booking detail page style starts from here */
.my_booking_details_sec .inner_section_booking_buttons_wrapper {display: flex; gap: 10px; align-items: center; margin-top: 25px;}
.my_booking_details_sec .inner_section_booking_buttons_wrapper .btn {width: 100%;}
.need_assistance_sec .inner_section_need_assistance_col .assitance_buttons_wrapper a {width: 220px;}

/* My booking detail page style ends here */

/* Error pages */
body:has(.error-page) .footer, body:has(.error-page) .navbar>div *:not(.navbar-brand, .navbar-brand > *) { display: none !important;}
body:has(.error-page) .navbar>div { justify-content: center; align-items: center;}
.error-page .button { font-weight: 600;}
.full-height {  height: 80vh;}
.fs-100 { font-size: 100px;}


.detail_contact_host .detail_contact_host_btn { background-color: var(--btn-yellow); border-color: var(--btn-yellow); }
.detail_contact_host .detail_contact_host_btn:hover { background-color: #fff; border-color: transparent; }


.bookings { min-height: 50.6vh; }
/* .product_box .listing_desc {  display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden; word-break: break-all; margin-bottom: 0;} */
.product_box .listing_desc p { margin-bottom: 0;}

.custom-cards input + .error-message { font-size: 12px; position: absolute; bottom: -6px;}

.booking_info .info .input_wrapper .input_parent label { border: 1px solid #4D4D4D; padding: 6px 20px; border-radius: 10px; width: 100%; text-align: center;  font-size: 14px;}
.booking_info .info .input_wrapper .input_parent input:checked + label { background-color: var(--btn-yellow);  border-color: var(--btn-yellow); }
.booking_info .info .input_wrapper .input_parent { width: 45%; }


.booking_info .info:has(.input_wrapper .input_parent input[id="same_day"]:checked) .check-cont { display: none;}

.booking_info .info input[type="time"] { position: relative; }
.booking_info .info input[type="time"]::-webkit-calendar-picker-indicator {background: transparent; height: 100%; bottom: 0;cursor: pointer;left: 0;position: absolute;right: 0;top: 0;width: auto;}

.booking_info:has(.tour_type #guests:checked) .type_private { display: none;}
.booking_info:has(.tour_type #private:checked) .type_guest { display: none;}

/* Flatpickr */
.flatpickr-calendar {border-radius: 10px; overflow: hidden; box-shadow: 6px 6px 10px rgba(0,0,0,0.2); border: 0.5px solid black;}
.flatpickr-calendar .flatpickr-time {background-color: #ffce32;}
.flatpickr-calendar .flatpickr-time .numInput:hover, .flatpickr-calendar .flatpickr-time .numInput:focus, .flatpickr-calendar .flatpickr-time .flatpickr-am-pm:focus, .flatpickr-calendar .flatpickr-time .flatpickr-am-pm:hover {background-color: #ffce32; box-shadow: inset 20px 20px 35px #ffce32;}
.flatpickr-calendar .flatpickr-time .numInput, .flatpickr-calendar .flatpickr-time .flatpickr-am-pm {font-family: "poppins"; font-weight: 700;}
.sec2_detail .book .info .book_timing input::placeholder { font-size: 15px;}


/* Tour Detail */
.tabs .nav-tabs .nav-item .nav-link { border: 2px solid transparent; border-radius: 8px; background-color: #EBEBEB; padding-inline: 25px; cursor: pointer; color: #000;}
.tabs .nav-tabs .nav-item .nav-link.active { border: 2px solid var(--dark-yellow); background-color: var(--dark-yellow); }

.tabs .tab-pane li p { margin: 0;}

.listing_itinerary .tabs .nav-tabs:not(:has(li:nth-child(2))) { display: none;}

/* Tour Confirm */

.edit-trip.button:hover { box-shadow: none; padding-block: 0;}
.edit-trip.button { padding: 0; border-radius: 0;}

/* Booking archive */
.review-not-btn { border-bottom: 1.5px solid var(--btn-yellow); min-width: 72px;}

.review_main_card .property-image { height: 178px; width: 300px;}
.review_main_card .user_img { height: 50px; width: 50px; overflow: hidden; border-radius: 50%;}
.review_main_card .user_img img { height: 100%;}

/* Review Stars */

.rating-option-sinlge .rating_star_wrapper label {  float: right; color: #bebebe; margin-right: 20px; }
.rating-option-sinlge .rating_star_wrapper label:first-of-type { margin-right: 0; }
.rating-option-sinlge .rating_star_wrapper input:checked ~ label > i { color: var(--btn-yellow);}
.rating-option-sinlge .rating_star_wrapper input:checked ~ label i { color: var(--btn-yellow); }
.rating-option-sinlge .rating_star_wrapper label:hover ~ label i, .rating-option-sinlge label:hover i {  color: var(--light-yellow);}


/* .images_wrapper .inner_section_main_col { max-width: 70%; margin: 0 auto;} */
/* .images_wrapper .drag_drop_photos_wrapper .add_photo_box { height: 400px; width: 100%; border: 2px dashed #c2c2c2; display: flex; flex-direction: column; justify-content: center; padding: 20px; gap: 80px; border-radius: 20px;}
.images_wrapper .drag_drop_photos_wrapper .add_photo_box .photo_icon { width: 110px; height: 110px; overflow: hidden; margin: 0 auto;}
.images_wrapper .drag_drop_photos_wrapper .add_photo_box .photo_icon img { width: 100%; height: 100%; object-fit: contain; object-position: center;}
.images_wrapper .drag_drop_photos_wrapper .add_photo_box .add_photo_btn label { color: #4A4A4A; font-family: Poppins; font-size: 13px; font-style: normal; font-weight: 600; line-height: 0; border-radius: 33px; background: var(--btn-yellow); padding: 25px 30px; cursor: pointer;}
.images_wrapper .drag_drop_photos_wrapper .add_photo_box .add_photo_btn input[type="file"], .images_wrapper .main_step_title, .images_wrapper .step_description { display: none;}

.images_wrapper .drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box .add_photo_btn { text-align: center;}
.images_wrapper .drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box .add_photo_btn .plus_icon_lbl {display: none}
.images_wrapper .drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .photo_icon {display: none;}
.images_wrapper .drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .plus_icon_lbl {display: block;}
.images_wrapper .drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .add_photos_lbl {display: none;} */


/*  Review Preview */
.sec1_detail .review_wrapper .user_img { height: 40px; width: 40px;}
.sec1_detail .review_wrapper .review_body .user_img { height: 30px;  width: 30px;}

.sec1_detail .review_wrapper .review_body input { width: 80%; border: 0; border-bottom: 1px solid #aeaeae; border-radius: 0; padding-left: 0; padding-bottom: 3px;}


.bar_rating_parent { width: 85%; margin-left: 20px;}
.bar_rating { height: 4px; border-radius: 6px; background-color: #c2c2c254;  width: 100%; }
.bar_rating.filled { position: absolute; top: 13px; left: 0; background-color: var(--btn-yellow); }


/* Images uploader style starts from here */

/* .listing_stepper .add_photos_step .inner_section_main_col {max-width: 70%; margin: 0 auto;} */
.drag_drop_photos_wrapper .add_photo_box {height: 400px; width: 100%; border: 2px dashed #c2c2c2; display: flex; flex-direction: column; justify-content: center; padding: 20px; gap: 80px}
.drag_drop_photos_wrapper .add_photo_box .photo_icon {width: 110px; height: 110px; overflow: hidden; margin: 0 auto;}
.drag_drop_photos_wrapper .add_photo_box .photo_icon img {width: 100%; height: 100%; object-fit: contain; object-position: center;}
.drag_drop_photos_wrapper .add_photo_box .add_photo_btn label {color: #4A4A4A; font-family: Poppins; font-size: 13px; font-style: normal; font-weight: 600; line-height: 0; border-radius: 33px; background: #ffce32; padding: 25px 30px; cursor: pointer;}
.drag_drop_photos_wrapper .add_photo_box .add_photo_btn input[type="file"] {display: none;}
.drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box .add_photo_btn { text-align: center;}

.drag_drop_photos_wrapper {display: flex; flex-wrap: wrap; gap: 20px 15px; /*max-height: calc(100vh - 420px);*/ overflow: hidden; overflow-y: auto; padding-right: 8px;}
.drag_drop_photos_wrapper .drag_drop_photo_single {flex: 0 0 23.5%; overflow: visible; border-radius: 20px; height: 200px; overflow: hidden;}
.drag_drop_photos_wrapper .drag_drop_photo_single img {width: 100%; height: 100%; object-fit: cover; object-position: center;}
/* .drag_drop_photos_wrapper .drag_drop_photo_single:first-child {flex: 0 0 100%; height: 450px;} */
.drag_drop_photos_wrapper:not(:has(.drag_drop_photo_single:nth-child(2))) .drag_drop_photo_single:first-child {flex: 0 0 100%; height: 100%;}
/* .drag_drop_photos_wrapper:not(:has(.drag_drop_photo_single:nth-child(2))) .drag_drop_photo_single:first-child {height: 100%;} */
.drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box .add_photo_btn .plus_icon_lbl {display: none}
.drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .photo_icon {display: none;}
.drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .plus_icon_lbl {display: block;}
.drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .add_photos_lbl {display: none;}

.drag_drop_photos_wrapper .drag_drop_photo_single, .listing_stepper .add_videos_step .video_wrapper .video_single {position: relative;}
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown, .listing_stepper .add_videos_step .video_wrapper .video_single .delete, .drag_drop_photos_wrapper .drag_drop_photo_single .bulk_del {position: absolute; right: 15px; top: 15px; z-index: 9;}
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown button, .listing_stepper .add_videos_step .video_wrapper .delete button  {background-color: #ffffffb3; color: #000; border-radius: 50%; box-shadow: 0 0 10px #0000000d; backdrop-filter:blur(2px); padding: 0; width: 35px; height: 35px; font-size: 15px;}
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu, .listing_stepper .add_videos_step .video_wrapper .video_single .delete .dropdown-menu {border-radius: 15px; box-shadow: 0 0 10px #0000000d; padding: 0;}
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu li:first-child a, .listing_stepper .add_videos_step .video_wrapper .video_single .delete .dropdown-menu a {border-top-left-radius: 15px; border-top-right-radius: 15px;}
/* .drag_drop_photos_wrapper .drag_drop_photo_single:first-child .dropdown-menu li .make_cover_btn {display: none;} */
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu li:last-child a {border-bottom-left-radius: 15px; border-bottom-right-radius: 15px;}
.drag_drop_photos_wrapper .drag_drop_photo_single:first-child .dropdown-menu li a, .listing_stepper .add_videos_step .video_wrapper .delete .dropdown-menu a:hover {border-radius: 15px;}
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu li a {font-family: "Poppins"; font-size: 13px; text-align: left; font-weight: 500; color: #000; padding: 9px 20px;}
/* .drag_drop_photos_wrapper .drag_drop_photo_single:first-child:not(:last-child):before { content: "Cover Photo"; display: block; position: absolute; top: 10px; left: 10px; background: #fffc; backdrop-filter: blur(5px); padding: 8px 15px; color: #000; font-family: 'Poppins'; font-weight: 500; border-radius: 10px; box-shadow: 0 0 10px #0000000d; font-size: 14px;} */

.drag_drop_photos_wrapper .add_photo_box .add_photo_btn .plus_icon_lbl {background: none;}
.drag_drop_photos_wrapper .add_photo_box .plus_icon_lbl i:before {border: 0; font-size: 50px; color: #a4a4a4;}

.drag_drop_photos_wrapper .drag_drop_photo_single .error-message { background-color: #e7a5a5; color: red; text-align: center; padding: 5px; max-width: 90%; font-size: 12px; position: absolute; bottom: 10px; right: 0; left: 0; margin: 0 auto; border-radius: 5px;}
/* .drag_drop_photos_wrapper .drag_drop_photo_single.warning .error-message { background-color: #e7e3a5; color: yellow; } */
#review.modal:has(.drag_drop_photos_wrapper .error-message) button[type="submit"] { background-color: transparent !important; border-color: var(--light-grey) !important; pointer-events: none; cursor: not-allowed;}

/* Add Review modal style starts from here */

#review.modal .modal-body {padding-top: 0; padding-right: 30px;}
#review.modal .review_comment .chat-input {flex: 1; border-radius: 20px;}
#review.modal .review_comment button[type="submit"]:not([disabled]), #review_edit_modal .review_comment button[type="submit"]:not([disabled]) {background-color: #ffce32; border: 1px solid #ffce32;}
#review.modal .review_comment button[type="submit"][disabled] {cursor: not-allowed;}
#review.modal .main_cart { margin-bottom: 0.5em; }

.listing_detail_page .listing_review .rating {display: flex; align-items: center; gap: 5px;}
#preview_review .review_sidebar .modal-title {line-height: 55px;}
#preview_review .modal-body .review_body {padding-top: 15px;}
#preview_review .modal-body .review_body .rating_star {color: #ffce32;}

#preview_review .modal-body .review_body .review_wrapper { height: 370px; overflow: hidden; overflow-y: auto; padding-right: 15px; margin-top: 20px; }
#preview_review.review .modal-body {overflow: unset;}

#preview_review .review_wrapper .reviews .image_wrapper { display: flex; gap: 5px; align-items: center; flex-wrap: wrap; }
#preview_review .review_wrapper .reviews .image_wrapper a { display: block; width: 60px; height: 60px; overflow: hidden; border-radius: 5px; }
#preview_review .review_wrapper .reviews .image_wrapper a img { width: 100%; height: 100%; object-fit: cover; object-position: center; }

.filter_dropdown .dropdown-btn { text-decoration: none; border: 1.5px solid #5c5c5c82; border-radius: 50px; padding: 8px 20px;}
.filter_dropdown .dropdown-menu { border-radius: 12px;}
.filter_dropdown .dropdown-item:hover { background-color: var(--btn-yellow);}

.custom_radio {position: relative;}
.custom_radio input {display: none;}
.custom_radio label {padding-left: 30px; margin: 3px 0}
.custom_radio label:before {content: ""; display: block; width: 20px; height: 20px; position: absolute; left: 0; top: 0; bottom: 0; margin: auto 0; border: 1px solid black; border-radius: 50%}
.custom_radio:has(input[type="radio"]:checked) label:after {content: ""; display: block; width: 19px; height: 19px; position: absolute; left: .5px; top: 0; bottom: 0; margin: auto 0; background: #ffce32; border: 2px solid white; outline: 2px solid #ffce32;border-radius: 50%;}
.custom_radio:has(input[type="radio"]:checked) label:before {content: unset;}

.review_body .comment-text .trans_btn { font-size: 13px; margin-bottom: 14px; color: #6a6a6a;}
.review_body .comment-text .trans_btn .translate_img { filter: invert(1);}


/* Calendar */

.daterangepicker.show-calendar .drp-buttons .drp-selected, .daterangepicker:before, .daterangepicker:after { display: none !important; }
.daterangepicker.show-calendar .drp-buttons .applyBtn { background-color: var(--btn-yellow); padding: 8px 20px; border-radius: 20px;  }
.daterangepicker .calendar-table th { color: #bababa;}
.daterangepicker .calendar-table th, .daterangepicker .calendar-table td { height: 30px; width: 30px; padding: 10px 0px; }
.daterangepicker th.month { font-family: 'Poppins'; font-size: 16px; font-weight: 600; color: #000; padding-top: 0;}
.daterangepicker.ltr.show-calendar { border-radius: 18px;  border: 0; box-shadow: 0 0 15px -5px #8e8e8e;}
.daterangepicker td.disabled, .daterangepicker option.disabled { text-decoration:  none; color: #999999bd;}
.daterangepicker td.in-range { background-color: #fcdf7352; }
/* .daterangepicker .calendar-table td.available { font-weight: 600;} */
.daterangepicker.show-calendar { padding: 15px;}
.daterangepicker th.next.available { padding-top: 0; }
.daterangepicker:not(.single) { width: 52%;}
.daterangepicker .drp-calendar { max-width: unset; min-width: 49%;}
.daterangepicker:not(.single) .drp-calendar.left { padding-right: 25px;}
.daterangepicker .drp-calendar.right { float: right;}
.daterangepicker.single .calendar-table th, .daterangepicker.single .calendar-table td { padding: 10px 14px;}


/* Top Bar filter home page */

.filter_wrapper { margin: 25px auto; border-radius: 50px; background-color: #fff;  box-shadow: 0 8px 20px 0 rgba(0, 0, 0, 0.07); }
.filter_wrapper .input_wrapper  { border: 0; border-radius: 50px; padding: 10px 30px;  transition: 0.3s ease-in; position: relative; cursor: pointer;}
/* .filter_wrapper .input_wrapper:hover , .filter_wrapper .input_wrapper:has(.dropdown-menu.show, input:focus)  { background-color: var(--btn-yellow); transition: 0.3s ease-in; } */
.filter_wrapper .input_wrapper:hover input::placeholder { color: #0f0f0f; transition: 0.3s ease-in;}
.filter_wrapper .input_wrapper input { border: 0; background-color: transparent; padding: 0; font-size: 14px; margin-top: 5px; box-shadow: none; transition: 0.3s ease-in; color: #4A4A4A;}
.filter_wrapper .input_wrapper .d-btn input::placeholder{color:#4A4A4A}
.filter_wrapper .input_wrapper label { font-size: 14px; cursor: pointer; color: #4A4A4A;}
.filter_wrapper .input_wrapper .number_guests { color: #4A4A4A;}
.filter_wrapper .input_wrapper.button_wrapper .trans_btn { padding: 10px 0; height: 40px; width: 40px; text-align: center; border-radius: 50%; background-color: var(--black); color: #fff; }
.filter_wrapper .input_wrapper.button_wrapper .submit .trans_btn { border: 1px solid var(--black); width: 40px; color: var(--black); background-color: transparent; cursor: pointer; }
.filter_wrapper .input_wrapper.button_wrapper .submit input[disabled] + .trans_btn { cursor: no-drop; opacity: 0.35;  transition: 0.3s ease-in; }
.filter_wrapper .input_wrapper.button_wrapper .submit input:not([disabled]) + .trans_btn { background-color: var(--btn-yellow); transition: 0.3s ease-in;}
.filter_wrapper .input_wrapper .input_guests { width: 60%; }
.filter_wrapper .input_wrapper .remove_btn { opacity: 0; transition: 0.3s ease-in; cursor: pointer; }
.filter_wrapper .input_wrapper .d-btn:has(input:focus) + .remove_btn, .remove_btn.show { opacity: 1; transition: 0.3s ease-in; }

.topbar #search-box .input_wrapper .remove_btn { color: red; top: 0; bottom: 0; height: fit-content; margin: auto 0; right: 30px; }
.filter_wrapper .input_wrapper .remove_btn.show { opacity: 1; transition: 0.3s ease-in;}

.topbar #search-box .input_wrapper .input_guests .guest_btn { color: rgb(110, 110, 110);}
.topbar #search-box .input_wrapper .input_guests .guest_btn span.guest_changed { color: #000; }


.filter_wrapper .input_wrapper.button_wrapper .guest_btn { background-color: transparent; width: 100%; text-align: start; border: 0; font-size: 14px;}
.filter_wrapper .input_wrapper.button_wrapper .members_wrapper:not(:first-child), .date_wrapper .rangePicker { display: none; }
.filter_wrapper .input_wrapper .d-btn input { width: 100%;padding-right: 20px;}

.filter_wrapper .input_wrapper .input_guests .dropdown-menu { border-radius: 15px; padding: 15px; border: 0; box-shadow: 0 10px 25px 0 rgba(0, 0, 0, 0.07); min-width: 330px; margin-top: 18px !important; right: 0 !important; transform: translate(0, 61px) !important;}
.filter_wrapper .input_wrapper .input_guests .dropdown {position: unset;}
.filter_wrapper .input_wrapper .input_guests .members_parnet { padding: 15px 5px;}
.filter_wrapper .input_wrapper .input_guests .members_parnet:not(:last-child) { border-bottom: 1px solid var(--grey);}
.filter_wrapper .input_wrapper .input_guests .members_parnet .guest_wrapper button { background-color: transparent; }

.topbar .filter_wrapper .input_wrapper:not(.single_input_wrapper) {height: 100%;}
.topbar .filter_wrapper .single_input_wrapper:not(.active) > .input_wrapper:hover {background-color: #ebebeb;}
.topbar .filter_wrapper .input_wrapper.single_input_wrapper:not(.single_input_wrapper.active):hover  {background-color: #ebebeb;}

.parent_view { min-height: 13.6em; }

.drag_drop_photos_wrapper .drag_drop_photo_single .delete_btn_wrapper {position: absolute; right: 15px; top: 15px; z-index: 9;}
.drag_drop_photos_wrapper .drag_drop_photo_single .delete_btn_wrapper .delete_btn {background-color: #ffffffb3; color: red; border-radius: 50%; box-shadow: 0 0 10px #0000000d; backdrop-filter:blur(2px); padding: 0; width: 35px; height: 35px; font-size: 15px; display: flex; align-items: center; justify-content: center;}


.review .review_comment .comment_wrapper {flex: 1;}
.review .review_comment .comment_wrapper .ck-editor .ck-content {border-radius: 15px}
.review .review_comment .comment_wrapper .ck-editor .ck-toolbar {border: 0;}
.review .review_comment button[type="submit"] {width: 100%;}
.review .review_comment .ck-editor__editable {min-height: 150px !important;}

.edit-reply-btn[disabled] { opacity: 0.5; cursor: no-drop;}
.replied_parent .replied .button_wrapper > .edit-review:not([data-update="0"]) { display: none;}


/* Jodit content editor view styling starts from here */

.help_center .card .card-footer a {text-decoration: none;}

/* Jodit content editor view styling ends here */


.image_uploader .card-title {  font-size: 26px;  font-weight: 600;}
.image-preview {  position: relative;display: inline-block; margin-bottom: 10px;}
.image-preview img {  object-fit: cover; border-radius: 5px; height: 250px; width: 100%; margin-bottom: 20px;}
.image-preview .error-message { position: absolute;  bottom: 10px; left: 25px; background: #e1505ec9; font-size: 12px; color: #fff; padding: 0px 5px; border-radius: 5px; max-width: 83%;}
.remove-image {  position: absolute; top: 10px; right: 25px; color: #E1505E !important; background: #fff; border-radius: 50%; width: 22px; height: 22px; text-align: center; padding-top: 1px; cursor: pointer; font-size: 13px;}

.topbar #search-box .date_wrapper .flatpickr-wrapper {width: 100%;}
/* .topbar:has(#box-1:checked) #search-box .date_input_wrapper .flatpickr-wrapper {display: none;} */

/* .flatpickr-calendar, .flatpickr-rContainer, .flatpickr-days { width: 700px !important; } */
.flatpickr-day.flatpickr-disabled, .flatpickr-day.flatpickr-disabled:hover { color: rgba(57,57,57,0.4);}

body .flatpickr-innerContainer { padding-top: 15px; }
body .dayContainer { max-width: 400px; min-width: 400px; justify-content: start;}
body .flatpickr-day { min-width: 56px; min-height: 54px; padding-top: 9px; margin: 10px 0; margin-top: 1px; box-shadow: none !important;}

/* body .flatpickr-calendar .flatpickr-day.flatpickr-disabled:nth-last-child(1) { background-color: #FFCE32; border-radius: 50px; color: #000;} */

/* Hide wishlist when count 0 */
.wishlist .badge:has(#wishlist_count[data-count="0"]) { display: none;}


#add_card .card-img-parent .cards-img { height: 32px; width: 50px; }
#add_card .card-img-parent .cards-img img { height: 100%; width: 100%; object-fit: contain; object-position: center; }

body:has(.hourly_slots) .select2-container--default .select2-results__option[aria-disabled=true]:not(li:first-child) { display: none; }


body .pac-container { border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); padding-top: 6px; top: 285px !important; width: fit-content !important; /* width: 620px !important; */ }
body .pac-container .pac-item { padding-block: 3px; font-size: 14px; }
body .pac-container .pac-item:first-child { border-top: 0; }
body .pac-container .pac-item:last-child { padding-bottom: 0; }
body .pac-container .pac-item:hover { background-color: var(--btn-yellow); }
body .pac-container.pac-logo:after { display: none !important; }
body .pac-container .pac-icon { background-image: url('../images/location-dot.svg'); background-size: contain; background-position: center; background-repeat: no-repeat; }

body:not(:has(.listing_detail_page)) .flatpickr-calendar { /* top: calc(100% + 15px); */ top: 285px !important;}

/* Accordion Profile Setting */

.profile_setting.accordion .accordion-item { box-shadow: -4px 4px 30px #********; border-radius: 20px; padding: 20px; }
.profile_setting.accordion .accordion-item .accordion-collapse, .profile_setting.accordion .accordion-item .accordion-button { border: 0;}

.profile_setting.accordion .accordion-header button { font-size: 18px; font-weight: 600; }
.accordion-item .accordion-button:after { background-image: url(../images/arrow-down.svg);}

.profile_setting .table td { font-weight: 400;}
.profile_setting .table td .default_badge { min-height: 28px; padding-block: 8px}
.profile_setting .table td .btn { border-radius: 100px; padding: 4px 33px; font-size: 13px;}


.setting_page .profile_setting .profile_setting_top_buttons_wrapper button {height: 45px; padding-block: 0;}
.setting_page .profile_setting .profile_setting_top_buttons_wrapper .delete_account_btn {height: 45px; padding-block: 0; display: flex; justify-content: center; align-items: center;}

.setting_page .profile_setting .profile_setting_top_buttons_wrapper a, .setting_page .profile_setting .profile_setting_top_buttons_wrapper button {border-radius: 70px;}

/* Home page pagination style starts from here */

#listing_row .product_box .swiper-button-prev, .show_map_sectiom .product_box .swiper-button-prev {left: -30px; transition: .3s ease; font-size: 30px; width: 30px; height: 28px;}
#listing_row .product_box .product_img:hover .swiper-button-prev, .show_map_sectiom .product_box .product_img:hover .swiper-button-prev {left: 10px; transition: .3s ease;}
#listing_row .product_box .swiper-button-next, .show_map_sectiom .product_box .swiper-button-next {right: -30px; transition: .3s ease; font-size: 30px; width: 30px; height: 28px;}
#listing_row .product_box .product_img:hover .swiper-button-next, .show_map_sectiom .product_box .product_img:hover .swiper-button-next {right: 10px; transition: .3s ease;}
#listing_row .product_box .product_img .swiper-pagination .swiper-pagination-bullet-active, .show_map_sectiom .product_box .product_img .swiper-pagination .swiper-pagination-bullet-active {background: #ffce32 !important;}

/* Ends here */


.bookings .booking_card_wrapper .booked_card .parent_flag .flag {border: 1px solid black;}
.bookings .booking_card_wrapper .booked_card .property-asset2 .listing_name { text-decoration: underline; text-underline-position: under; text-decoration-color: #fed402; text-decoration-thickness: 1.5px; font-size: 12px;}

.error_message_wrapper {background-color: #f8d7da; border: 1px solid #f5c2c7; border-radius: 5px; padding: 8px 5px 8px 5px; text-align: left; margin-bottom: 10px;}
.error_message_wrapper span {font-size: 13px; font-family: "Poppins"; text-align: left; color: #842029; position: relative; display: block;}
.error_message_wrapper span ul {padding-left: 20px; margin-bottom: 0;}
.error_message_wrapper span li {padding-left: 10px;}
.error_message_wrapper span li::marker {content: "\f057"; color: #842029; font-weight: 600; font-family: 'Font Awesome 5 Free'; position: absolute; left: 0; top: 0; height: fit-content;}
/* .error_message_wrapper span:before { content: '\f057'; position: absolute; left: 0; color: #842029; font-weight: 600; font-family: 'Font Awesome 5 Free'; top: 0; height: fit-content} */

.single_input_wrapper { position: relative; z-index: 100; cursor: pointer; height: 73px }
.highlight-bar { position: absolute; background-color: rgb(255 206 50); z-index: 1; transition: all 0.5s ease; border-radius: 50px; }

/* Additional Styling Searchbox */

/* body {transition: .2s ease;}
body:has(.topbar .filter_wrapper input:focus):before {content: ""; display: block; position: fixed; width: 100%; height: 100%; top: 0; left: 0; background: rgba(0,0,0,0.4); z-index: 200; transition: .2s ease;}
.topbar:has(input:focus) .filter_wrapper { background-color: #ffffff; z-index: 9999; position: relative;} */

/* .topbar .filter_wrapper {background-color: red;} */


/* Topbar calendar custom styling starts from here */

body .flatpickr-days:has(.selected.startRange):has(.selected.endRange) .flatpickr-day.selected.startRange:before, body .flatpickr-days:has(.selected.startRange):has(.selected.endRange) .flatpickr-day.selected.endRange:before { content: ""; width: 100%; height: 100%; display: block; position: absolute; top: 0; left: 0; background-color: #ffeaa4; z-index: -1}
body .flatpickr-day {border: 0;}
body .flatpickr-day.selected.startRange.endRange:before {content: unset;}
body .flatpickr-day.startRange:before {border-radius: 30px 0 0 30px;}
body .flatpickr-day.endRange:before {border-radius: 0 30px 30px 0;}
body .flatpickr-calendar .flatpickr-day.startRange, body .flatpickr-calendar .flatpickr-day.endRange {border-radius: 50%}
/* body .flatpickr-day.selected.startRange {pointer-events: none;} */

body .flatpickr-day.inRange {background: unset; border-color: unset;}
body .flatpickr-days:has(.selected.startRange):has(.selected.endRange) .flatpickr-day.inRange {background: #ffeaa4; border-color: #ffeaa4;}
body .flatpickr-day.startRange:not(.selected), body .flatpickr-day.endRange:not(.selected) {background: transparent; border: 1px solid transparent;}
body .flatpickr-day.startRange:not(.selected):hover, body .flatpickr-day.endRange:not(.selected):hover {background: #ffce3240; border: 1px solid #FFCE32;}

/* Topbar calendar custom styling ends here */


/* Messenger page custom styling starts from here */

.messenger_main_sec {padding: 10px 0 0 0}
/* .messenger_main_sec .inner_section_messenger_main_col {box-shadow: 0 0 8px #BDBDBD40; padding: 10px 15px; border-radius: 33px;} */
.messenger_main_sec .main_heading_wrapper {display: flex; justify-content: space-between; align-items: center; margin-bottom: 50px; padding-bottom: 20px; border-bottom: 1px solid #BDBDBD;}
.messenger_main_sec .messenger_wrapper *:not(i) {font-family: 'Poppins-Regular';}
.messenger_main_sec .messenger_wrapper .btn:focus {box-shadow: none}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills {gap: 10px; /* border-bottom: 1px solid #DEDEDE; padding-bottom: 15px; */}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item {flex: 1; border: 1px solid #FFCE32; border-radius: 35px; height: 45px; font-size: 14px;}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item button {width: 100%;min-width: max-content; background: none; color: #FFCE32; height: 100%}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item:has(button.active) {background-color: #FFCE32; border-color: #FFCE32}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item button.active {color: #4A4A4A;}

/* .chat_threads_search:has(.start_new_convo_btn) {display: flex; gap: 5px; align-items: center;} */

.messenger_main_sec .inner_section_messenger_sidebar {padding-right: 5px; border-right: 1px solid #DEDEDE;}
.messenger_main_sec .messenger_wrapper .inner_section_chat_threads .chat_threads_search {position: relative; overflow: hidden;}
.messenger_main_sec .messenger_wrapper .inner_section_chat_threads .chat_threads_search i {position: absolute; top: 0; bottom: 0; left: 15px; height: fit-content; margin: auto 0; color: #4A4A4A;}
.messenger_main_sec .messenger_wrapper .inner_section_chat_threads .chat_threads_search input {height: 45px; border-radius: 33px; padding-left: 40px; font-size: 14px;}
.messenger_main_sec .messenger_wrapper .chat_threads_wrapper {display: flex; flex-direction: column; gap: 6px; margin-top: 20px; padding-bottom: 0; height: 915px; overflow-y: auto;}
.messenger_main_sec .messenger_wrapper .chat_threads_wrapper .single_chat_thread {display: flex; gap: 10px; align-items: center; border-radius: 12px; box-shadow: 0 0 8px #BDBDBD1A; padding: 10px}
.messenger_main_sec .messenger_wrapper .user_profile_picture {width: 55px; height: 55px; overflow: hidden; flex-shrink: 0;}
.messenger_main_sec .messenger_wrapper .user_profile_picture img {width: 100%; height: 100%; object-fit: cover; object-position: center;}
.messenger_main_sec .messenger_wrapper .chat_details_wrapper {flex: 1;}
.messenger_main_sec .messenger_wrapper .chat_details_wrapper .username_date_wrapper {display: flex; justify-content: space-between; align-items: center;}
.messenger_main_sec .messenger_wrapper .chat_details_wrapper .username_date_wrapper h6 {margin-bottom: 0; font-size: 15px; font-family: 'Poppins-SemiBold';}
.messenger_main_sec .messenger_wrapper .chat_details_wrapper .username_date_wrapper span {font-size: 12px; color: #8A8A8A;}
.messenger_main_sec .messenger_wrapper .open_chat_icon {display: flex; justify-content: flex-end}
.messenger_main_sec .messenger_wrapper .open_chat_icon i {font-size: 14px; line-height: 12px; color: #3C3C434D}
.messenger_main_sec .messenger_wrapper .chat_preview p {margin-bottom: 0; color: #616161; font-family: "Poppins"; font-size: 13px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 1; line-clamp: 1; -webkit-box-orient: vertical;}
.messenger_main_sec .messenger_wrapper .inner_section_chat_threads .chat_threads_search .start_new_convo_btn { width: 100%; height: 45px; border-radius: 33px; background-color: #FFCE32; display: flex; justify-content: center; align-items: center; font-size: 14px;}

.messenger_main_sec .inner_section_chat_messages .chat_top_bar {display: flex; justify-content: space-between; align-items: center; box-shadow: 0 0 4px #BDBDBD40; border-radius: 13px; padding: 10px 20px; /* margin-bottom: 30px; */ position: relative;}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar .profile_picture_username_wrapper {display: flex; align-items: center; gap: 10px}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar .profile_picture_username_wrapper .user_profile_picture {width: 55px; height: 55px; overflow: hidden;}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar .profile_picture_username_wrapper .user_profile_picture img {width: 100%; height: 100%; object-fit: cover; object-position: center;}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar .profile_picture_username_wrapper .username h6 {margin-bottom: 0; font-family: 'Poppins-SemiBold';}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar .chat_options {display: flex; align-items: center; gap: 10px}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar .chat_options .find_messages_btn, .messenger_main_sec .inner_section_chat_messages .chat_top_bar .chat_options button {width: 40px; height: 40px; background: #FFCE32; padding: 0; border-radius: 50%; display: flex; justify-content: center; align-items: center; text-decoration: none; color: #4A4A4A}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar .chat_options button {box-shadow: none;}

.messenger_main_sec .inner_section_chat_messages .chat_top_bar .chat_options .call_us_now_btn {background-color: #FFCE32; border-radius: 33px; font-size: 13px; height: 40px; display: flex ; justify-content: center; align-items: center;}

.messenger_main_sec .messenger_wrapper .chat_footer {display: flex; align-items: center; gap: 10px;}
.messenger_main_sec .messenger_wrapper .chat_footer .typing_attchment_wrapper {position: relative; display: flex; align-items: center; width: 100%;}
.messenger_main_sec .messenger_wrapper .chat_footer .typing_attchment_wrapper input {flex: 1; width: 100%; border: 1.15px solid #E3E5E5; height: 60px; border-radius: 33px}
.messenger_main_sec .messenger_wrapper .chat_footer .typing_attchment_wrapper .dropdown {position: absolute; right: 0; top: 0; bottom: 0; margin: auto 0; height: fit-content}
.messenger_main_sec .messenger_wrapper .chat_footer .typing_attchment_wrapper .dropdown button {color: #72777A; font-size: 20px;}
.messenger_main_sec .messenger_wrapper .chat_footer .send_button .send_msg_btn {height: 60px; width: 60px; border-radius: 50px; background-color: #FFCE32; display: flex; justify-content: center; align-items: center; text-decoration: none; font-size: 20px; border: 0;}
.messenger_main_sec .messenger_wrapper .chat_footer .send_button .send_msg_btn[disabled] {background-color: #e5e5e5;}

.messenger_main_sec .messenger_wrapper .chat_body {height: 870px; padding-bottom: 30px; overflow-y: auto; padding-right: 10px;}
.messenger_main_sec .messenger_wrapper .chat_body .inner_body {display: flex; flex-direction: column; justify-content: flex-end; gap: 5px; text-align: center; min-height: 100%;}
.messenger_main_sec .messenger_wrapper .chat_body .msg .chat_bubble {background: #ffe998; width: fit-content; padding: 13px 15px; border-radius: 33px;}
.messenger_main_sec .messenger_wrapper .chat_body .msg .chat_bubble p {margin-bottom: 0; font-size: 14px;}
.messenger_main_sec .messenger_wrapper .chat_body .msg_right .chat_bubble {margin-left: auto;}
.messenger_main_sec .messenger_wrapper .chat_body .msg_left .chat_bubble {background-color: #F2F4F5;}

.messenger_main_sec .messenger_wrapper .chat_body .msg {display: flex; align-items: flex-end; gap: 5px;}
.messenger_main_sec .messenger_wrapper .chat_body .msg .user_image {width: 35px; height: 35px; overflow: hidden;}
.messenger_main_sec .messenger_wrapper .chat_body .msg .user_image img {width: 100%; height: 100%; object-fit: cover; object-position: center;}
.messenger_main_sec .messenger_wrapper .chat_body .msg_left:not(:has(.user_image)) {padding-left: 40px}
.messenger_main_sec .messenger_wrapper .chat_body .msg_right:not(:has(.user_image)) {padding-right: 40px}

.messenger_main_sec .inner_section_chat_messages .chat_top_bar .chat_options .dropdown-menu .dropdown-item {display: flex; /*justify-content: space-between;*/ align-items: center; gap: 10px;}
.messenger_main_sec .messenger_wrapper .chat_footer .typing_attchment_wrapper .dropdown .dropdown-item {display: flex; /*justify-content: space-between;*/ align-items: center; gap: 10px;}

.messenger_main_sec .inner_section_chat_messages .chat_top_bar .chat_options .dropdown-menu .dropdown-item span:first-child {flex: 1; display: flex; align-items: center;}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar .chat_options .dropdown-menu .dropdown-item span:nth-child(2) {flex: 5; font-size: 15px;}

.messenger_main_sec .inner_section_chat_messages .chat_top_bar .chat_options .dropdown-menu .dropdown-item span:first-child img {width: 20px; height: 16px; object-fit: contain;}

.messenger_main_sec .messenger_wrapper .chat_footer .typing_attchment_wrapper .dropdown .dropdown-item span:first-child {flex: 1; display: flex; align-items: center;}
.messenger_main_sec .messenger_wrapper .chat_footer .typing_attchment_wrapper .dropdown .dropdown-item span:nth-child(2) {flex: 5; font-size: 15px;}

.messenger_main_sec .messenger_wrapper .chat_footer .typing_attchment_wrapper .dropdown .dropdown-item span:first-child img {width: 20px; height: 16px; object-fit: contain;}

body .swal2-container button:where(.swal2-styled).swal-confirm-phone-verified { font-family: 'Poppins-Medium'; border-radius: 40px; font-size: 14px;}

.header .user_img li a img { border-radius: 0; width: 20px; height: 18px; object-fit: contain;}
.header .user_img li a {display: flex; gap: 5px;}
.header .user_img li a span:first-child {flex: 1;}
.header .user_img li a span:nth-child(2) {flex: 6;}
.header .user_img li a.dropdown-item span:first-child {flex: none;}

.messenger_main_sec .inner_section_chat_messages .chat_top_bar  .chat_inner_search {width: 350px; background: #fff; color: #000; font-family: "Poppins-Medium"; border-radius: 10px; border: 0;}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar  .chat_inner_search .heading_cross_wrapper {display: flex; justify-content: space-between; margin-bottom: 5px; padding-right: 5px}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar  .chat_inner_search .heading_cross_wrapper h6 {font-family: "Poppins-Medium";}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar  .chat_inner_search .heading_cross_wrapper .close_search i {color: #000;}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar  .chat_inner_search .search_buttons_wrapper {position: relative;}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar  .chat_inner_search .search_buttons_wrapper input[type="search"] {padding-right: 60px; background: #fff; border: 1px solid #b6b6b6; height: 40px; color: #000; font-family: "Poppins-Regular"; border-radius: 30px;}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar  .chat_inner_search .search_buttons_wrapper .search_above {position: absolute; top: 0; bottom: 0; margin: auto 0; height: fit-content; right: 35px; color: #000; font-size: 12px; width: 18px; height: 18px;}
.messenger_main_sec .inner_section_chat_messages .chat_top_bar  .chat_inner_search .search_buttons_wrapper .search_below {position: absolute; top: 0; bottom: 0; margin: auto 0; height: fit-content; right: 15px; color: #000; font-size: 12px; width: 18px; height: 18px;}

.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item #conversationDropdown {color: black; flex: 1;}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item:has(#conversationDropdown) {display: flex; position: relative;}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item #conversationDropdown {position: absolute;right: 0;top: 0;bottom: 0;width: 100%; text-align: right;}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item button {flex: 10;}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item:has(#conversationDropdown) .dropdown-menu .dropdown-item.active {background-color: #FFCE32; color: #000;}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item #conversationDropdown {color: #FFCE32;}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item:has(#pills-conversations-tab.active) #conversationDropdown {color: #000;}

.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item #pills-conversations-tab img {filter: brightness(0) saturate(100%) invert(79%) sepia(96%) saturate(472%) hue-rotate(328deg) brightness(101%) contrast(101%);}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item:has(#pills-conversations-tab.active) #pills-conversations-tab img {filter: unset;}

.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item:has(#conversationDropdown) #pills-conversations-tab { display: flex; justify-content: center; align-items: center; gap: 10px; }
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item:has(#conversationDropdown) .filter-option {display: flex; align-items: center; gap: 10px; padding: .25rem 1rem; justify-content: flex-start; font-size: 15px; font-family: 'Poppins-Regular';}
.messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item:has(#conversationDropdown) .filter-option img {width: 17px; height: 17px;}

.messenger_main_sec .messenger_wrapper .chat_preview {display: flex; justify-content: space-between; align-items: center; gap: 10px;}
.messenger_main_sec .messenger_wrapper .chat_preview .user-role-badge {font-weight: 400; font-size: 8px;}

/* Messenger page custom styling ends here */

/* .show_map_sectiom {max-height: 100vh;position: sticky;top: 0;right: 0;} */
.show_map_sectiom {max-height: 90vh; position: sticky; top: 30px; right: 0;}
.messenger_main_sec #conversationDropdown::before{content:"\f107";font-family: 'Font Awesome 5 Free'; font-weight: 900;font-size: 16px;}
.messenger_main_sec #conversationDropdown.show::before{content:"\f106";}
.messenger_main_sec #conversationDropdown::after{display: none;}
#deleteAccountModal .toggle-password{position: absolute; top: 45px; right: 15px; cursor: pointer;}
#password-part:has(.is-invalid) .toggle-password {right: 35px;}



.setting_page .select2.select2-container {height: 50px; margin-bottom: 5px; border: 0; padding: 0;}
.setting_page .select2.select2-container .selection {height: 100%; display: block;}
.setting_page .select2.select2-container .select2-selection {height: 100%; display: flex; align-items: center;  border-radius: 15px; border: 1px solid #ced4da;}
.setting_page .select2.select2-container .select2-selection__arrow {height: 50px;}
.setting_page .country-select-wrapper .select2.select2-container .select2-selection__arrow {right: 20px;}

.setting_page .select2.select2-container.select2-container--below.select2-container--open .select2-selection {border-bottom-left-radius: 0; border-bottom-right-radius: 0; border-bottom: 0;}
.setting_page .select2.select2-container.select2-container--above.select2-container--open .select2-selection {border-top-left-radius: 0; border-top-right-radius: 0; border-top: 0;}

body:has(.setting_page) .select2-dropdown {border: 1px solid #ced4da;}
body:has(.setting_page .select2.select2-container.select2-container--below.select2-container--open) .select2-dropdown {border-top: 0; box-shadow: none; border-top-left-radius: 0; border-top-right-radius: 0; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;}
body:has(.setting_page .select2.select2-container.select2-container--above.select2-container--open) .select2-dropdown {border-bottom: 0; box-shadow: none; border-bottom-left-radius: 0; border-bottom-right-radius: 0; border-top-left-radius: 10px; border-top-right-radius: 10px;}

#listing_map .map_product .product_box a {text-align: left;}

header .dropdown.globe .dropdown-menu { margin-top: 11px; right: 0; left: unset; filter: drop-shadow(2px 4px 6px rgba(0,0,0,0.2))}
header .dropdown.globe .dropdown-menu .lang-pic .flag_img { width: 30px; height: 20px; overflow: hidden; }
header .dropdown.globe .dropdown-menu .lang-pic .flag_img img { width: 100%; height: 100%; object-fit: fill; object-position: center; }

.filter_wrapper .input_wrapper .input_guests .dropdown-menu:has(.members_wrapper.car[style *= "display: block;"]) {min-width: 450px; transform: translate(-120px, 61px) !important;}
.filter_wrapper .input_wrapper .input_guests .dropdown-menu:has(.members_wrapper.car[style *= "display: block;"]) .time_field_wrapper {flex: 3}
.filter_wrapper .input_wrapper .input_guests .dropdown-menu:has(.members_wrapper.car[style *= "display: block;"]) .member_heading {flex: 2;}

/* KYC verfication modal style starts from here */

#verificationStepperModal .modal-dialog {width: 60%; max-width: 60%}
#verificationStepperModal .modal-body #verification-stepper {max-width: 100%;margin: 0 auto;}
#verificationStepperModal .modal-body #verification-stepper .line {margin: 0;}
#verificationStepperModal .modal-body .bs-stepper-header {max-width: 70%; margin: 0 auto; gap: 15px;}
#verificationStepperModal .modal-body .bs-stepper-header .step {/*border: 2px dashed #00000036;*/ border-radius: 15px;}
/* #verificationStepperModal .modal-body .bs-stepper-header .bs-stepper-label {color: black;} */
#verificationStepperModal .modal-body .bs-stepper-content {padding-bottom: 0;}
#verificationStepperModal .modal-body .bs-stepper-header .step .step-trigger {padding: 8px;}
body .verified .bs-stepper-circle {background-color: #187c19 !important;font-size: 0; position: relative;}
/* body .verified .bs-stepper-circle::before{content:"✔"; position:absolute;font-size:16px;} */
body .verified .bs-stepper-circle::before { content: "\f058"; position: absolute; font-size: 18px; font-family: 'Font Awesome 5 Free'; color: white; left: 0; right: 0; top: 0; bottom: 0; margin: auto; width: 100%; height: 100%; display: flex ; justify-content: center; align-items: center; }
#verificationStepperModal .modal-body .bs-stepper-header .step .step-trigger{background:none;color: unset;}

/* KYC verfication modal style ends here */

/* Optimistic messaging styles */
.optimistic-message .chat_bubble {
    opacity: 0.8;
    position: relative;
}

.optimistic-message .message-attachment img {
    opacity: 0.7;
}

.failed-message .chat_bubble {
    background-color: #ffebee !important;
    border: 1px solid #e57373;
}

.failed-message .sending-indicator {
    color: #e74c3c !important;
}

.retry-btn {
    background: none !important;
    border: none !important;
    color: #3498db !important;
    cursor: pointer !important;
    font-size: 11px !important;
    text-decoration: underline !important;
    padding: 0 !important;
    margin-left: 5px !important;
}

.retry-btn:hover {
    color: #2980b9 !important;
}

.sending-indicator {
    font-size: 11px;
    color: #999;
    margin-left: 5px;
}

.sending-indicator i {
    margin-right: 3px;
}

/* Message time styling */
.message-time {
    font-size: 11px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Chat date time styling */
.chat_date_time {
    text-align: center;
    margin: 15px 0;
}

.chat_date_time p {
    background-color: #f0f0f0;
    color: #666;
    padding: 5px 15px;
    border-radius: 15px;
    display: inline-block;
    font-size: 12px;
    margin: 0;
}



.bookings #booking-card-container .property-asset2 > div {flex-shrink: 0;}
.bookings #booking-card-container .property-asset2 > a {flex-shrink: 0 !important;flex: 1.5;}
.bookings #booking-card-container .property-asset2 > a > div {width: 105px;}



/* Signup country select styles */

#signUp .flag-icon {
    width: 20px;
    height: 15px;
    margin-right: 8px;
    display: inline-block;
    background-size: cover;
    background-position: center;
    border-radius: 2px;
}

#signUp .country-select-wrapper {
    position: relative;
}

#signUp #country-select-signup {
    padding-left: 40px;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 12px;
    padding-right: 40px;
}

#signUp .selected-flag {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 2;
}

#signUp .country-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
}

#signUp .country-option:hover {
    background-color: #f8f9fa;
}


#signUp .select2.select2-container {height: 42.6px; margin-bottom: 5px; border: 0; padding: 0;}
#signUp .select2.select2-container .selection {height: 100%; display: block;}
#signUp .select2.select2-container .select2-selection {height: 100%; display: flex; align-items: center;  border-radius: 15px; border: 1px solid #ced4da;}
#signUp .select2.select2-container .select2-selection__arrow {height: 50px;}

#signUp .select2.select2-container.select2-container--below.select2-container--open .select2-selection {border-bottom-left-radius: 0; border-bottom-right-radius: 0; border-bottom: 0;}
#signUp .select2.select2-container.select2-container--above.select2-container--open .select2-selection {border-top-left-radius: 0; border-top-right-radius: 0; border-top: 0;}

/* #signUp .country-select-wrapper .select2 {display: flex; align-items: center;} */
/* #signUp .country-select-wrapper .select2 .selection {height: fit-content;} */
#signUp .country-select-wrapper .select2 .select2-selection {background-color: unset; border: 0; border-radius: 0; /*height: fit-content;*/}
#signUp .country-select-wrapper .select2 .select2-selection:focus-visible {border: 0; outline: none;}
#signUp .country-select-wrapper .select2 {border: 1px solid var(--light-grey); border-radius: 12px; padding: 0 20px; text-align: left;}
#signUp .country-select-wrapper .select2 .select2-selection__rendered {padding: 0; font-size: 14px; line-height: 16px;}
#signUp .country-select-wrapper .select2 .select2-selection__arrow {top: 0; bottom: 0; margin: auto 0; right: 13px;}


.bookings .booking_card_wrapper .edit-review-btn {border-bottom: 1.5px solid #000;}

#signUp .modal-dialog {max-width: 760px;}
#signUp .modal-dialog .modal-content {padding-block: 10px !important;}
#signUp .modal-dialog .modal-content .modal-header {padding-block: 10px;}

.setting_page .profile_setting .session_history_accordion table tr th, .setting_page .profile_setting .session_history_accordion table tr td {text-align: center;}


/* Delete account modal stepper header style fixing starts from here */

#deleteAccountModal .modal-dialog {max-width: 650px;}
#deleteAccountModal .modal-body .bs-stepper-header .step {flex: 0 0 155px; width: 155px; flex-shrink: 0;}
#deleteAccountModal .modal-body .bs-stepper-header .step button {padding: 20px 10px; flex-direction: column; width: 100%;}
#deleteAccountModal .modal-body .bs-stepper-header .line {margin: 0 5px;}

/* Delete account modal stepper style ends here */


/* KYC Verification popup style starts from here */

#verificationStepperModal #verification-stepper #phone-verification-part .main_image_wrapper, #verificationStepperModal #verification-stepper #kyc-verification-part .main_image_wrapper {height: 250px; width: 100%; overflow: hidden; margin-bottom: 20px;}
#verificationStepperModal #verification-stepper #phone-verification-part .main_image_wrapper img, #verificationStepperModal #verification-stepper #kyc-verification-part .main_image_wrapper img {width: 100%; height: 100%; object-fit: contain; object-position: center;}
#verificationStepperModal #verification-stepper #phone-verification-part .phone_verification_container h5, #verificationStepperModal #verification-stepper #kyc-verification-part h5 {font-size: 30px; font-family: "Poppins-Medium"; margin-bottom: 20px}
#verificationStepperModal #verification-stepper #phone-verification-part .phone_verification_container p, #verificationStepperModal #verification-stepper #kyc-verification-part p {font-size: 18px; font-family: "Poppins-Medium";}
#verificationStepperModal #verification-stepper #phone-verification-part .phone_verification_container p span.phone_number {color: #E0AD0B;}
#verificationStepperModal #verification-stepper #phone-verification-part .phone_verification_container #change_phone_link {color: #E0AD0B;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container .radio_input_wrapper label, #verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-otp-container .radio_input_wrapper label {position: relative; padding-left: 30px; cursor: pointer;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container .radio_input_wrapper label:before, #verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-otp-container .radio_input_wrapper label:before {content: ""; display: block; position: absolute; width: 20px; height: 20px; border-radius: 50%; border: 2px solid black; left: 0; top: 0; bottom: 0; margin: auto 0;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container .radio_input_wrapper label:has(input:checked):before, #verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-otp-container .radio_input_wrapper label:has(input:checked):before {border-color: #E6B20B;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container .radio_input_wrapper label:has(input:checked):after, #verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-otp-container .radio_input_wrapper label:has(input:checked):after {content: ""; display: block; position: absolute; width: 12px; height: 12px; background-color: #E6B20B; border-radius: 50%; left: 4px; top: 0; bottom: 0; margin: auto 0;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container .radio_input_wrapper label input, #verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-otp-container .radio_input_wrapper label input {display: none;}
#verificationStepperModal #verification-stepper #phone-verification-part .phone_verification_container p.different_phone_number_text {font-size: 16px;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container #stepper_send_otp_button, #verificationStepperModal #verification-stepper #kyc-verification-part a {margin-top: 20px; width: fit-content; height: 57px; display: inline-flex; justify-content: center; align-items: center;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container, #verificationStepperModal #verification-stepper #kyc-verification-part {text-align: center;}
#verificationStepperModal .modal-header {border-bottom: 0;}
#verificationStepperModal .modal-header .btn-close {width: 15px; height: 15px; background-color: #ffce32; border-radius: 50%; font-size: 10px; opacity: 1; z-index: 999;}
#verificationStepperModal #verification-stepper #kyc-verification-part p {max-width: 75%; margin: 0 auto;}

#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container .disclaimer_wrapper {margin-top: 20px}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container .disclaimer_wrapper label input {display: none;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container .disclaimer_wrapper label {position: relative; padding-left: 25px; font-style: italic; cursor: pointer; text-align: left;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container .disclaimer_wrapper label:before {content: ""; display: block; position: absolute; width: 18px; height: 18px; border-radius: 4px; border: 2px solid black; left: 0; top: 3px;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container .disclaimer_wrapper label:has(input[type="checkbox"]:checked):before {border: 2px solid #e6b20b;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container .disclaimer_wrapper label:has(input[type="checkbox"]:checked):after {content: "\f00c"; font-family: "Font Awesome 5 Free"; font-weight: 900; display: block; position: absolute; width: 18px; height: 18px; background-color: #e6b20b; border-radius: 4px; left: 0; top: 3px; display: flex; justify-content: center; align-items: center; color: #000}

#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container #stepper_send_otp_button {opacity: 0.7; pointer-events: none; background-color: #E6E6E6;}
#verificationStepperModal #verification-stepper #phone-verification-part #phone-verification-send-container:has(.disclaimer_wrapper label input[type="checkbox"]:checked) #stepper_send_otp_button {opacity: 1; pointer-events: auto; background-color: var(--btn-yellow, #FFCE32);}

#verificationStepperModal #verification-stepper #phone-verification-otp-container .try_another_methor a {color: #ffce32; font-family: "Poppins-Regular";}

#verificationStepperModal .modal-body {padding-top: 15px;}

/* KYC verification popup style ends here */


/* Detail page input fields spanish placeholder style starts */

html[lang="es"] .sec2_detail .book .info .book_timing input::placeholder {font-size: 13px;}

/* Detail page input fields spanish placeholder style ends */

/* Help center page style starts */

.help_center .box_parent.list-view .box-view-item .card .searched_faq_wrapper {padding-top: 20px;}
.help_center .box_parent.list-view .box-view-item .card .searched_faq_wrapper .single_filtered_faq {padding: 10px; border-radius: 5px; border: 1px solid #ffce3269; background: white}
.help_center .box_parent.list-view .box-view-item .card .searched_faq_wrapper .single_filtered_faq:not(:first-child) {margin-top: 15px}
.help_center .box_parent.list-view .box-view-item .card .searched_faq_wrapper .single_filtered_faq .faq_description {overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; line-clamp: 2; -webkit-box-orient: vertical; margin-bottom: 0; font-size: 13px; word-break: break-all; color: #000;}
.help_center .box_parent.list-view .box-view-item .card .searched_faq_wrapper .single_filtered_faq .faq_title {color: #ffce32;}

.help_center .box_parent.list-view .box-view-item .card .searched_faq_wrapper .single_filtered_faq a:hover .faq_title {text-decoration: underline; transition: .3s ease;}

/* Help center page style ends */



header [tooltip] {position: relative;}
header [tooltip]::before, header [tooltip]::after { text-transform: none; font-size: .9em; line-height: 1; user-select: none; pointer-events: none; position: absolute; display: none; opacity: 0; }
header [tooltip]::before { content: ''; border: 5px solid transparent; z-index: 1001; }
header [tooltip]::after {content: attr(tooltip); font-family: Helvetica, sans-serif; text-align: center; min-width: 3em; max-width: 21em; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; padding: 1ch 1.5ch; border-radius: .3ch; box-shadow: 0 1em 2em -.5em rgba(0, 0, 0, 0.35); background: #333; color: #fff; z-index: 1000; }
header [tooltip]:hover::before, header [tooltip]:hover::after { display: block; }
header [tooltip='']::before, header [tooltip='']::after { display: none !important; }

header [tooltip]:not([flow])::before, header [tooltip][flow^="up"]::before { bottom: 100%; border-bottom-width: 0; border-top-color: #333; }
header [tooltip]:not([flow])::after, header [tooltip][flow^="up"]::after { bottom: calc(100% + 5px); }
header [tooltip]:not([flow])::before, header [tooltip]:not([flow])::after, header [tooltip][flow^="up"]::before, header [tooltip][flow^="up"]::after { left: 50%; transform: translate(-50%, -.5em); }

header [tooltip][flow^="down"]::before { top: 100%; border-top-width: 0; border-bottom-color: #333; }
header [tooltip][flow^="down"]::after {top: calc(100% + 5px);}
header [tooltip][flow^="down"]::before, header [tooltip][flow^="down"]::after { left: 50%; transform: translate(-50%, .5em); }

header [tooltip][flow^="left"]::before { top: 50%; border-right-width: 0; border-left-color: #333; left: calc(0em - 5px); transform: translate(-.5em, -50%); }
header [tooltip][flow^="left"]::after { top: 50%; right: calc(100% + 5px); transform: translate(-.5em, -50%); }

header [tooltip][flow^="right"]::before { top: 50%; border-left-width: 0; border-right-color: #333; right: calc(0em - 5px); transform: translate(.5em, -50%); }
header [tooltip][flow^="right"]::after { top: 50%; left: calc(100% + 5px); transform: translate(.5em, -50%); }

header [tooltip]::after {border-radius: 10px; font-size: 13px; font-family: 'Poppins-Regular';}
/* header [tooltip][flow^="down"]::before {top: 75%;}
header [tooltip][flow^="down"]::after {top: calc(75% + 5px);} */

@keyframes tooltips-vert {
    to {
        opacity: .9;
        transform: translate(-50%, 0);
    }
}

@keyframes tooltips-horz {
    to {
        opacity: .9;
        transform: translate(0, -50%);
    }
}

header [tooltip]:not([flow]):hover::before, header [tooltip]:not([flow]):hover::after, header [tooltip][flow^="up"]:hover::before, header [tooltip][flow^="up"]:hover::after, header [tooltip][flow^="down"]:hover::before, header [tooltip][flow^="down"]:hover::after { animation: tooltips-vert 300ms ease-out forwards; }
header [tooltip][flow^="left"]:hover::before, header [tooltip][flow^="left"]:hover::after, header [tooltip][flow^="right"]:hover::before, header [tooltip][flow^="right"]:hover::after { animation: tooltips-horz 300ms ease-out forwards; }


.header .navbar .nav-item:has(#languageDropdown) li.active a.lang-pic {background-color: var(--btn-yellow);}

#verificationStepperModal #verification-stepper #phone-verification-part {padding-block: 0;}
#verificationStepperModal #verification-stepper #phone-verification-part .phone_verification_container h5 {margin-bottom: 15px}
#verificationStepperModal #verification-stepper #phone-verification-part .phone_verification_container p {margin-bottom: 8px;}

.wishlist_main_sec .empty_wishlist_text { display: flex; justify-content: center; align-items: center; height: 50vh; font-size: 30px; font-weight: bold; }

/* Header Listing search UI starts from here */

header .search_parent #listing-search-dropdown .listing_search_item_wrapper .listing_search_item {gap: 15px}
header .search_parent #listing-search-dropdown .listing_search_item_wrapper .listing_img_cat_badge_wrapper {position: relative;}
header .search_parent #listing-search-dropdown .listing_search_item_wrapper .listing_img_cat_badge_wrapper .category_badge {width: 25px; height: 25px; overflow: hidden; background-color: #ffce32; display: flex; position: absolute; right: -10px; top: -8px; justify-content: center; align-items: center; border-radius: 50%; border: 1px solid black;}
header .search_parent #listing-search-dropdown .listing_search_item_wrapper .listing_img_cat_badge_wrapper .category_badge img {width: 60%; height: 60%; object-fit: contain; object-position: center; filter: brightness(0) saturate(100%) invert(0%) sepia(83%) saturate(12%) hue-rotate(199deg) brightness(96%) contrast(100%);}
header .search_parent #listing-search-dropdown .listing_search_item_wrapper .listing_detail .hosted_by {font-size: 13px; display: block;}
header .search_parent #listing-search-dropdown .listing_search_item_wrapper .listing_img_cat_badge_wrapper .listing_search_image {object-fit: cover; object-position: center;}