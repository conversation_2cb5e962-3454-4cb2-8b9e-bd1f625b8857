@push('css')
    <style>
        .white_card {
            padding: 20px;
        }
        .badge{
            text-transform: none !important
        }

        #languageTabs {
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 0;
        }

        #languageTabs li {
            margin-bottom: -2px;
        }

        #languageTabs li a {
            background-color: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
            border-radius: 8px 8px 0 0;
            padding: 12px 24px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        #languageTabs li a:hover {
            background-color: #e9ecef;
            color: #495057;
            text-decoration: none;
        }

        #languageTabs li.active a,
        #languageTabs li.active a:hover,
        #languageTabs li.active a:focus {
            background-color: #ffce32;
            color: #000;
            border-color: #ffce32;
            border-radius: 8px 8px 0 0;
        }

        .tab-content {
            padding: 20px 0;
        }

        .form_field_padding {
            margin-bottom: 20px;
        }

        .form_field_padding label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .form-control {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 12px 15px;
            font-size: 14px;
        }

        .form-control:focus {
            border-color: #ffce32;
            box-shadow: 0 0 0 0.2rem rgba(255, 206, 50, 0.25);
        }

        .btn.create_btn {
            background-color: #ffce32;
            border: none;
            color: #000;
            padding: 12px 30px;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn.create_btn:hover {
            background-color: #e6b82e;
            transform: translateY(-1px);
        }

        .help-block {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
        }

        .has-error .form-control {
            border-color: #dc3545;
        }

        .notification-key-field {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .available-placeholders {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 6px;
            margin-top: 10px;
        }

        .placeholder-tag {
            background-color: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin: 2px;
            display: inline-block;
        }

        .placeholder-input-container {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 8px;
            min-height: 50px;
            background-color: #fff;
            cursor: text;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            gap: 5px;
        }

        .placeholder-input-container:focus-within {
            border-color: #ffce32;
            box-shadow: 0 0 0 0.2rem rgba(255, 206, 50, 0.25);
        }

        .placeholder-badge {
            background-color: #ffce32;
            color: #000;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            margin: 2px;
        }

        .placeholder-badge .remove-badge {
            cursor: pointer;
            font-weight: bold;
            color: #666;
            margin-left: 3px;
        }

        .placeholder-badge .remove-badge:hover {
            color: #000;
        }

        .placeholder-input {
            border: none;
            outline: none;
            background: transparent;
            flex: 1;
            min-width: 120px;
            padding: 4px;
            font-size: 14px;
        }

        .placeholder-display {
            font-family: monospace;
            color: #666;
            font-size: 11px;
        }
    </style>
@endpush

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="white_card card_height_100 mb_30">
                <div class="white_card_header">
                    <div class="box_header m-0">
                        <div class="main-title">
                            <h3 class="m-0">{{ isset($template) ? 'Edit' : 'Create' }} Notification Template</h3>
                        </div>
                        <div class="header_more_tool">
                            <a href="{{ route('notification-templates.index') }}" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Back
                            </a>
                        </div>
                    </div>
                </div>
                <div class="white_card_body">
                    <form
                        action="{{ isset($template) ? route('notification-templates.update', $template->ids) : route('notification-templates.store') }}"
                        method="POST">
                        @csrf
                        @if (isset($template))
                            @method('PUT')
                        @endif

                        <div id="inputFormRow">
                            <!-- Notification Key Field -->
                            <div class="notification-key-field">
                                <div class="form_field_padding {{ $errors->has('key') ? 'has-error' : '' }}">
                                    <label for="key">Notification Key *</label>
                                    <input type="text" class="form-control" name="key" id="key"
                                        value="{{ old('key', $template->key ?? '') }}"
                                        placeholder="e.g., booking_placed, user_registered"
                                        {{ isset($template) && $template->type === 'system' ? 'readonly' : '' }}>
                                    {!! $errors->first('key', '<p class="help-block">:message</p>') !!}
                                    <small class="text-muted">
                                        This key will be used in your code to reference this notification template.
                                    </small>
                                </div>

                                @if (!isset($template))
                                    <div class="form_field_padding">
                                        <label for="type">Template Type</label>
                                        <select class="form-control" name="type" id="type">
                                            <option value="system" selected>System</option>
                                            <option value="custom">Custom</option>
                                        </select>
                                    </div>
                                @endif
                            </div>

                            <!-- Placeholders Field -->
                            <div class="form_field_padding">
                                <label for="placeholders">Available Placeholders</label>
                                <div class="placeholder-input-container" id="placeholderContainer">
                                    <input type="text" class="placeholder-input" id="placeholderInput"
                                        placeholder="Type placeholder name and press Enter">
                                </div>
                                <small class="text-muted">
                                    Type placeholder names and press Enter to add them. Only lowercase letters and numbers allowed. (eg. customer name, listing name etc)
                                </small>
                                <div id="placeholderDisplay" class="placeholder-display mt-2"></div>
                                    <!-- Container for displaying badges -->
                                    <div id="badgeContainer">
                                        @forelse ($template->placeholders ?? [] as $placeholder)
                                            <div>
                                                <input type="hidden" name="placeholders[]" value="{{ $placeholder }}">
                                                <span class="badge">{{ $placeholder }}</span>
                                                <button type="button" class="remove-badge">x</button>
                                            </div>
                                        @empty
                                            
                                        @endforelse
                                    </div>
                            </div>

                            {{-- Channel Field for checkbox (app, email, sms, push, whatsapp) --}}
                            <div class="form_field_padding">
                                <label for="channels">Channels</label>
                                @foreach (['app', 'email', 'sms', 'push', 'whatsapp'] as $channel )
                                    <div class="form-check">
                                        <label class="form-check-label" for="{{ $channel }}">
                                            {{ ucfirst($channel == 'app' ? "In App" : $channel) }}
                                            <input class="form-check-input" type="checkbox" name="channels[]" id="{{ $channel }}" value="{{ $channel }}" {{ ($template->$channel ?? 0) == 1 ? 'checked' : '' }}>
                                            </label>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Language Tabs -->
                            <ul class="nav nav-tabs" id="languageTabs" role="tablist">
                                <li role="presentation" class="active">
                                    <a href="#en" aria-controls="en" role="tab" data-toggle="tab">English</a>
                                </li>
                                <li role="presentation">
                                    <a href="#es" aria-controls="es" role="tab" data-toggle="tab">Español</a>
                                </li>
                            </ul>

                            <!-- Tab Content -->
                            <div class="tab-content" id="languageTabContent">
                                <!-- English Tab -->
                                @foreach (["en", "es"] as $lang)
                                    <div role="tabpanel" class="tab-pane {{ app()->getLocale() == $lang ? 'active' : '' }} " id="{{ $lang }}">
                                        <div
                                            class="form_field_padding {{ $errors->has('translations.'.$lang .'.title') ? 'has-error' : '' }}">
                                            <label for="title_{{ $lang }}">Title *</label>
                                            <input type="text" class="form-control" name="translations[{{ $lang }}][title]"
                                                id="title_{{ $lang }}"
                                                value="{{ old('translations.' . $lang . '.title', isset($template) ? $template->getTranslation($lang)->title : '') }}"
                                                placeholder="E.g., Booking Confirmation">
                                            {!! $errors->first('translations.' . $lang . '.title', '<p class="help-block">:message</p>') !!}
                                        </div>
                                        <div
                                            class="form_field_padding {{ $errors->has('translations.'.$lang .'.short') ? 'has-error' : '' }}">
                                            <label for="short_{{ $lang }}">Short *</label>
                                            <input type="text" class="form-control" name="translations[{{ $lang }}][short]"
                                                id="short_{{ $lang }}"
                                                value="{{ old('translations.' . $lang .'.short', isset($template) ? $template->getTranslation($lang)->short : '') }}"
                                                placeholder="E.g., Booking Confirmation">
                                            {!! $errors->first('translations.' . $lang .'.short', '<p class="help-block">:message</p>') !!}
                                        </div>
                                        <div
                                            class="form_field_padding {{ $errors->has('translations.'.$lang .'.expanded') ? 'has-error' : '' }}">
                                            <label for="expanded_{{ $lang }}">Expanded *</label>
                                            <textarea class="form-control" name="translations[{{ $lang }}][expanded]" id="expanded_{{ $lang }}" rows="4"
                                                placeholder="Your booking has been confirmed...">{{ old('translations.' . $lang .'.expanded', isset($template) ? $template->getTranslation($lang)->expanded : '') }}</textarea>
                                            {!! $errors->first('translations.' . $lang .'.expanded', '<p class="help-block">:message</p>') !!}
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="form_field_padding">
                                <div class="list_form_btn">
                                    <input class="btn create_btn" type="submit"
                                        value="{{ isset($template) ? 'Update Template' : 'Create Template' }}">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('js')
    @verbatim
        <script>
            $(document).ready(function() {
                $('#placeholderInput').on('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault(); // Prevent form submission

                        var inputValue = $(this).val();

                        // Remove anything that is not a letter or number, replace spaces with underscores
                        var formattedValue = inputValue.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');

                        if (formattedValue) {
                            // Check if the badge already exists
                            if ($('#badgeContainer .badge').text().includes(formattedValue)) {
                                alert("This badge already exists!");
                            } else {
                                // Add a new badge below the input field
                                $('#badgeContainer').append(
                                    `<div>
                                        <input type="hidden" name="placeholders[]" value="${formattedValue}">
                                        <span class="badge">{{${formattedValue}}}</span>
                                    </div>
                                    `
                                    );
                            }

                            // Clear the input field after adding the badge
                            $(this).val('');
                        }
                    }
                });

                $(document).on("click", ".remove-badge", function(e) {
                    e.preventDefault();
                    $(this).parent().remove();
                });
            });
        </script>

    @endverbatim
@endpush
