<?php

namespace App\Http\Controllers;

use App\Http\Requests\NotificationTemplateRequest;
use App\Models\NotificationTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NotificationTemplateController extends Controller
{
    public function index()
    {
        $templates = NotificationTemplate::with('translations')->get();
        return view('dashboard.notification-templates.index', compact('templates'));
    }

    public function create()
    {
        return view('dashboard.notification-templates.create');
    }

    public function store(NotificationTemplateRequest $request)
    {
        try {
            DB::beginTransaction();
            $templateData = [
                // remove space from key 
                'key' => str_replace(' ', '_', strtolower($request->key)),
                'type' => $request->type ?? 'system',
                'is_active' => $request->is_active ?? true,
                'placeholders' => $request->placeholders ?? [],
            ];

            $channels = ['whatsapp', 'email', 'push', 'sms', 'app'];
            foreach ($channels as $channel) {
                if (in_array($channel, $request->channels ?? [])) {
                    $templateData[$channel] = 1;
                } else {
                    $templateData[$channel] = 0;
                }
            }

            $template = NotificationTemplate::create($templateData);
            foreach ($request->translations as $locale => $translation) {
                $template->translateOrNew($locale)->title = $translation['title'] ?? "";
                $template->translateOrNew($locale)->short = $translation['short'] ?? "";
                $template->translateOrNew($locale)->expanded = $translation['expanded'] ?? "";
            }
            $template->save();
            DB::commit();
            return redirect()->route('notification-templates.index')
                ->with('success', 'Notification template created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with(["message" => $e->getMessage(), "type" => "error"]);
        }
    }

    public function edit($id)
    {
        $template = NotificationTemplate::with('translations')->where('ids', $id)->firstOrFail();
        return view('dashboard.notification-templates.edit', compact('template'));
    }

    public function update(NotificationTemplateRequest $request, $id)
    {
        $template = NotificationTemplate::where('ids', $id)->firstOrFail();

        // Update template with new placeholders
        $templateData = [
            // remove space from key 
            // 'key' => str_replace(' ', '_', strtolower($request->key)),
            // 'type' => $request->type ?? 'system',
            'is_active' => $request->is_active ?? true,
            'placeholders' => $request->placeholders ?? [],
        ];

        $channels = ['whatsapp', 'email', 'push', 'sms', 'app'];
        foreach ($channels as $channel) {
            if (in_array($channel, $request->channels ?? [])) {
                $templateData[$channel] = 1;
            } else {
                $templateData[$channel] = 0;
            }
        }
        $template->update($templateData);

        foreach ($request->translations as $locale => $translation) {
            $template->translateOrNew($locale)->title = $translation['title'] ?? "";
            $template->translateOrNew($locale)->short = $translation['short'] ?? "";
            $template->translateOrNew($locale)->expanded = $translation['expanded'] ?? "";
        }
        $template->save();

        return redirect()->route('notification-templates.index')
            ->with('success', 'Notification template updated successfully');
    }
    public function destroy($ids)
    {
        // $template = NotificationTemplate::findOrFail($id);
        try{
            $template = NotificationTemplate::where('ids', $ids)->where('type', '!=', 'system')->firstOrFail();
            $template->delete();
            return redirect()->route('notification-templates.index')
                ->with('success', 'Notification Deleted successfully');
        }catch(\Exception $e){
            return back()->with(["message" => $e->getMessage(), "type" => "error"]);
        }
    }
}
