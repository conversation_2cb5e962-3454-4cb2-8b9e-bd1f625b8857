<?php

namespace App\Listeners;

use App\Events\UserLoggedIn;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendLoginNotification
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\UserLoggedIn  $event
     * @return void
     */
    public function handle(UserLoggedIn $event)
    {
        $template = $event->template;
        $user = $event->user;
         // Replace placeholders with actual user data
        $message = $template->message;
        $message = str_replace(
            ['{{city}}', '{{country}}'],
            [$user->city, $user->country],
            $message
        );
         // Send In-App Notification if enabled
        if ($template->is_active && $template->app) {
            $user->notify(new UserLoggedInNotification($message));
        }

        // Send Email Notification if enabled
        if ($template->is_active && $template->email) {
            Mail::to($user->email)->send(new LoginNotification($message));
        }
    }
}
