<?php

namespace App\Http\Controllers\Api;

use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\{LoginRequest, RegisterUserRequest};
use App\Services\Api\{AuthService, VerificationService};
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{

    function __construct(
        protected AuthService $authService,
        protected VerificationService $verificationService
    ) {}


    public function createUser(RegisterUserRequest $request)
    {
        try {
            $user = User::where("email", $request->email)->first();
            if(isset($user) && $user->email_verified == 1){
                return api_response_json(false, "Email already verified", null, 400);
            }

            $this->verificationService->send_otp($request->email);
            if (!$user) {
                $this->authService->createUser($request->validated());
            }
            return api_response_json(true, "OTP sent successfully please check your email", null, 200);
        } catch (\Throwable $th) {
            return api_response_json(false, $th->getMessage(), null, 500);
        }
    }

    function checkEmailOtp(Request $request)
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'email'    => 'required|email|exists:users,email',
                'otp'    => 'required',
            ], [
                'email.exists' => 'The email does not exist in our records.'
            ]);
            if ($validator->fails()) {
                return api_response_json(false, $validator->errors()->first(), null, 400);
            }

            // Verify OTP
            $verify_otp = $this->verificationService->verify_otp($request->email, $request->otp);
            if ($verify_otp) {
                $user = User::with("roles")->where("email", $request->email)->first();
                $token = $user->createToken("API TOKEN")->plainTextToken;
                return api_response_json(true, "OTP verified successfully", ["user" => $user, "token" => $token], 200);
                // return api_response_json(true, "OTP verified");
            } else {
                return api_response_json(false, "Invalid OTP", null, 400);
            }
        } catch (\Throwable $th) {
            return api_response_json(false, $th->getMessage(), null, 500);
        }
    }
    function resendEmailOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email'    => 'required|email|exists:users,email',
            ], [
                'email.exists' => 'The email does not exist in our records.'
            ]);
            if ($validator->fails()) {
                return api_response_json(false, $validator->errors()->first(), null, 400);
            }
            $user = User::where("email", $request->email)->first();
            if ($user->email_verified == 1) {
                return api_response_json(false, "Email already verified", null, 400);
            }
            $send_otp = $this->verificationService->send_otp($request->email);
            if ($send_otp) {
                return api_response_json(true, "OTP sent successfully please check your email", null, 200);
            } else {
                return api_response_json(false, "Something went wrong", null, 500);
            }
        } catch (\Throwable $th) {
            return api_response_json(false, $th->getMessage(), null, 500);
        }
    }


    public function loginUser(LoginRequest $request)
    {
        try {
            $login = $this->authService->login($request->validated());
            $user = $login['user'];
            
            // Track user session for API login
            $sessionTrackingService = app(\App\Services\SessionTrackingService::class);
            $sessionTrackingService->createSession($request, $user);

            activity($user["name"])->performedOn($user)->causedBy($user)->log("LoggedIn");
            return api_response_json(true, "User Logged In Successfully", $login, 200);
        } catch (\Throwable $th) {
            return api_response_json(false, $th->getMessage(), null, 500);
        }
    }
    function forgetPassword(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email'    => 'required|email|exists:users,email',
            ], [
                'email.exists' => 'The email does not exist in our records.'
            ]);
            if ($validator->fails()) {
                return api_response_json(false, $validator->errors()->first(), null, 400);
            }
            $user = User::where("email", $request->email)->first();
            if ($user->email_verified == 0) {
                return api_response_json(false, "Email not verified", null, 400);
            }
            $send_password_otp = $this->verificationService->send_password_otp($request->email);
            if ($send_password_otp) {
                return api_response_json(true, "Password OTP sent successfully please check your email", null, 200);
            } else {
                return api_response_json(false, "Something went wrong", null, 500);
            }
        } catch (\Exception $e) {
            return api_response_json(false, $e->getMessage(), null, 500);
        }
    }
    function check_password_otp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email'    => 'required|email|exists:users,email',
                'otp'    => 'required',
            ], [
                'email.exists' => 'The email does not exist in our records.'
            ]);
            if ($validator->fails()) {
                return api_response_json(false, $validator->errors()->first(), null, 400);
            }
            $verify_otp = $this->verificationService->verify_otp(email: $request->email, otp: $request->otp, otp_type: "password");
            if ($verify_otp) {
                return api_response_json(true, "OTP verified successfully", null, 200);
            } else {
                return api_response_json(false, "Invalid OTP", null, 400);
            }
        } catch (\Exception $e) {
            return api_response_json(false, $e->getMessage(), null, 500);
        }
    }
    function resetPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email'    => 'required|email|exists:users,email',
            'new_password' => 'required|min:8|same:confirm_password',
            'confirm_password' => 'required|min:8'
        ], [
            'email.exists' => 'The email does not exist in our records.'
        ]);
        if ($validator->fails()) {
            return api_response_json(false, $validator->errors()->first(), null, 400);
        }

        try {
            $reset_password = $this->verificationService->reset_password($request->email, $request->new_password);
            if ($reset_password) {
                return api_response_json(true, "Password reset successfully", null, 200);
            } else {
                return api_response_json(false, "Please verify otp first", null, 500);
            }
        } catch (\Exception $e) {
            return api_response_json(false, $e->getMessage(), null, 500);
        }
    }
    function logout(){
        try {
            auth()->user()->currentAccessToken()->delete();
            return api_response_json(true, "User Logged Out Successfully", null, 200);
        } catch (\Throwable $th) {
            return api_response_json(false, $th->getMessage(), null, 500);
        }
    }
}
