<?php

namespace App\Providers;
// use File;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\File;
use Illuminate\Support\ServiceProvider;
use Illuminate\Pagination\Paginator;
use App\Listing;
use App\Observers\ListingObserver;
use App\Observers\UserObserver;
use App\User;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Listing::observe(ListingObserver::class);
        User::observe(UserObserver::class);
        logger("ListingObserver registered successfully");
        Paginator::useBootstrap();
        $menus = [];
        if (File::exists(base_path('resources/laravel-admin/menus.json'))) {
            $menus = json_decode(File::get(base_path('resources/laravel-admin/menus.json')));
            view()->share('laravelAdminMenus', $menus);
        }

        Blade::if("serviceProvider", function () {
            return Auth::user()->hasRole("service");
        });
        Blade::if("customer", function () {
            return Auth::user()->hasRole("customer");
        });

        $mailsetting = \App\Models\MailSetting::first();
        $common_settings = \App\CommonSetting::first();
        if ($mailsetting) {
            config([
                'mail.driver' => $mailsetting->mail_transport,
                'mail.host' => $mailsetting->mail_host,
                'mail.port' => $mailsetting->mail_port,
                'mail.username' => $mailsetting->mail_username,
                'mail.password' => $mailsetting->mail_password,
                'mail.encryption' => $mailsetting->mail_encryption,
                'mail.from' => [
                    'address' => $mailsetting->mail_from,
                    'name' => $common_settings->title
                ]
            ]);
        }

        if (!Auth::check()) {
            view()->share('countries', \App\Country::orderBy('name')->get());
        }

        // Set Carbon locale based on app locale
        $locale = app()->getLocale();
        \Carbon\Carbon::setLocale($locale);
        
        // Ensure Spanish locale is available
        if ($locale === 'es') {
            \Carbon\Carbon::setLocale('es');
        }
    }


    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(\App\Services\TwilioService::class, function ($app) {
            return new \App\Services\TwilioService();
        });
    }
}
