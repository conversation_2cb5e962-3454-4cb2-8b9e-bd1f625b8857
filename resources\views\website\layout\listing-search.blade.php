@forelse ($listings as $listing)
    @if ($listing->slug)
        <li class="listing_search_item_wrapper py-2  divider">
            <a style="text-decoration: none" class="d-flex align-items-center p-2 listing_search_item"
                href="{{ route('detail', ["locale" => app()->getLocale(), 'listing_id' => $listing->ids, 'slug' => $listing->slug]) }}">
                <div class="listing_img_cat_badge_wrapper">
                    <img class="listing_search_image" width="50" height="50"
                    src="{{ asset('website') . '/' . ($listing->thumbnail_image->url ?? "") }}" alt=""
                    onerror="this.onerror=null;this.src=`{{ asset('website') }}/images/plcaeholderListingImg.png`;">
                    <span class="category_badge">
                        <img src="{{ asset('website') }}/{{ $listing->category->image }}" alt="category image">
                    </span>
                </div>
                <div class="listing_detail">
                    <span class="listing_search_title">{{ $listing->name }} </span>
                    <span class="hosted_by">{{ translate('listing_details.hosted_by')  }} {{ $listing->user->first_name ?? '' }}</span>
                </div>
            </a>
        </li>
    @endif
@empty
    <li class="d-flex align-items-center p-2 listing_search_item">
        <a style="text-decoration: none" href="#">
            No listing found
        </a>
    </li>
@endforelse
